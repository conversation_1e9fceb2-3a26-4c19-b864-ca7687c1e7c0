import ctx from "../ctx";
import type { PointData } from "../types";
import {
  firstRenderSignalRootPoints,
  firstRenderMultiplyRootPoints,
  renderMoreData,
} from "./renderData";
import { getMaxY } from "../utils";
import { MODE } from "../enums";
import KsgGraph, { frameScheduler } from "./KsgGraph";

/**
 * @param pointsData 节点数据
 * @param totalPoints 总节点数
 * @param rootId 根节点id
 */
export async function loadPointsData(
  pointsData: PointData[],
  totalPoints: number,
  rootId?: string
) {
  ctx.graph = new KsgGraph(pointsData);
  // 数据计算完成
  frameScheduler.onCompleted(() => {
    ctx.maxLevelY = getMaxY(ctx.graph!, ctx.levelSpace as number);
    ctx.controls!.yMinRange = -ctx.maxLevelY - 20;
    ctx.pointsLevelPager!.total = totalPoints;

    ctx.pointsLevelPager!.current += ctx.pointsLevelPager!.levelSize;
    ctx.focusPointInfo!.pointId = rootId || "";
    // 渲染模式
    switch (ctx.model) {
      case MODE.Single_ROOT:
        firstRenderSignalRootPoints();
        break;
      case MODE.MULTIPLE_ROOT:
        firstRenderMultiplyRootPoints();
        break;
    }
  });
}

/**
 * 加载更多的数据
 */
export function loadMorePointsData(pointsData: PointData[]) {
  ctx.graph!.loadMore(pointsData).then((diffData) => {
    // 记录深度
    ctx.maxLevelY = getMaxY(ctx.graph!, ctx.levelSpace as number);
    // 更新控制器触底深度
    ctx.controls!.yMinRange = -ctx.maxLevelY - 6;
    const focusIndex = ctx.pointsMesh!.focusIndex;
    renderMoreData(focusIndex, diffData);
    // console.log("loadMorePointsData", ctx.graph);
  });
}
