import {
  TextureLoader,
  RepeatWrapping,
  InstancedMesh,
  SphereGeometry,
  ShaderMaterial,
  AdditiveBlending,
  Color,
} from "three";
import { studyStatusToColor } from "../utils";
import { Point } from "../types";
import focusCrustImg from "../assets/images/OIP-A.jfif";
import vertexShader from "../shader/vert.glsl?raw";
import fragmentShader from "../shader/frag.glsl?raw";

const textureLoader = new TextureLoader();
const focusTreTexture = textureLoader.load(focusCrustImg);
focusTreTexture.wrapS = focusTreTexture.wrapT = RepeatWrapping;
// 共享聚焦节点壳体
const haloGeo = new SphereGeometry(1, 30, 15);
export class FocusCrust extends InstancedMesh {
  lastPoint: Point | null = null;
  constructor(size: number, opacity: number = 1.0) {
    super(
      haloGeo,
      new ShaderMaterial({
        uniforms: {
          uThickness: {
            value: 1.8,
          },
          uNoiseTexture: {
            value: focusTreTexture,
          },
          uColor: {
            value: new Color(0xfff),
          },
          uTime: {
            value: 0,
          },
          opacity: {
            value: 1.0,
          },
        },
        vertexShader,
        fragmentShader,
        transparent: true,
        blending: AdditiveBlending,
        depthWrite: false,
      }),
      1
    );
    this.renderOrder = 2;
  }
  /**
   * 渲染帧更新函数
   * @param {number} delta 时间间隔
   */
  update(delta: number = 0.01) {
    if (this.visible) {
      (this.material as ShaderMaterial).uniforms.uTime.value += delta;
    }
  }

  /**
   * 把该模型绑定在聚焦节点上
   * @param {Point} bindPoint 聚焦节点的userData数据
   */
  display(bindPoint: Point) {
    this.position.set(...bindPoint.coordinate);
    (this.material as ShaderMaterial).uniforms.uColor.value =
      studyStatusToColor(bindPoint.status);
    this.visible = true;
    this.lastPoint = bindPoint;
  }

  /**
   * 隐藏
   */
  hide() {
    this.visible = false;
    this.lastPoint = null;
  }

  /**
   * 坐标更新
   * @param position 坐标
   */
  updatePosition(position: [number, number, number]) {
    this.position.set(...position);
  }
}
const focusCrust = new FocusCrust(1);
focusCrust.hide();

export default focusCrust;
