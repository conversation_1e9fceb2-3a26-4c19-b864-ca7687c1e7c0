import { CSS2DObject } from "three/examples/jsm/renderers/CSS2DRenderer.js";
import type { Point, ViewRange } from "../types";
import { renderMathJax, reFormateTitle } from "../utils/mathJax";

/**
 * 用于计算label尺寸
 */
const computedDom = document.createElement("div");
computedDom.className = "css2d-label-computed";
computedDom.style.opacity = "0";
document.body.appendChild(computedDom);

/**
 * 用于计算节点的世界坐标
 */

export class KsgLabel extends CSS2DObject {
  labelWidth: number = 0;
  labelHeight: number = 0;
  /**
   *当前悬浮节点
   */
  point: Point | null = null;
  /*上一次hover节点索引 */
  lastIndex: number | null = null;
  //label定位
  offset: { x: number; y: number } = { x: 0, y: 0 };
  constructor(offset: { x: number; y: number } = { x: 10, y: 10 }) {
    const element = document.createElement("div");
    element.className = "css2d-label";
    super(element);
    this.visible = false;
    this.center.set(0, 1); //右上
    this.offset = offset;
    this.element.style.setProperty("--ml", `${offset.x}px`);
    this.element.style.setProperty("--mb", `${offset.y}px`);
  }

  /**
   * label显示
   * @param {Point} point 当前label对应的point数据
   * @param {any} option 设置label出现位置
   */
  display(
    point: Point,
    option?: {
      viewRange: ViewRange;
      dnc: { x: number; y: number };
    }
  ) {
    if (this.lastIndex === point.index) return;
    this.point = point;
    this.element.innerHTML = `<div class='css2d-label-inner'>${reFormateTitle(
      point.name
    )}</div>`; //绑定数据
    this.element.setAttribute("id", point.id); //绑定id
    this.position.set(...point.coordinate); //初始化位置
    renderMathJax(this.element);
    computedDom.innerHTML = this.element.innerHTML;
    if (option) this.setPosition(option!);
    this.visible = true;
    this.lastIndex = point.index!;
  }

  private setPosition(option: {
    viewRange: ViewRange;
    dnc: { x: number; y: number };
  }) {
    //计算label宽高
    let { width, height } = computedDom.getBoundingClientRect();
    width += this.offset.x;
    height += this.offset.y;
    let position = "right";
    if (option.viewRange.minX + option.dnc.x + width >= option.viewRange.maxX) {
      position = "left";
    } else if (
      option.viewRange.minX + option.dnc.x - width <
      option.viewRange.minX
    ) {
      position = "right";
    }

    if (
      option.viewRange.minY + option.dnc.y + height >=
      option.viewRange.maxY
    ) {
      position += "top";
    } else if (
      option.viewRange.minY + option.dnc.y - height <
      option.viewRange.minY
    ) {
      position += "bottom";
    } else {
      position += "top";
    }

    switch (position) {
      case "lefttop":
        this.center.set(1, 1);
        this.element.style.setProperty("--mt", "0px");
        this.element.style.setProperty("--ml", "0px");
        this.element.style.setProperty("--mr", `${this.offset.x}px`);
        this.element.style.setProperty("--mb", `${this.offset.y}px`);
        break;
      case "leftbottom":
        this.center.set(1, 0);
        this.element.style.setProperty("--mb", "0px");
        this.element.style.setProperty("--ml", "0px");
        this.element.style.setProperty("--mr", `${this.offset.x}px`);
        this.element.style.setProperty("--mt", `${this.offset.y}px`);
        break;
      case "righttop":
        this.center.set(0, 1);
        this.element.style.setProperty("--mt", "0px");
        this.element.style.setProperty("--mr", "0px");
        this.element.style.setProperty("--ml", `${this.offset.x}px`);
        this.element.style.setProperty("--mb", `${this.offset.y}px`);
        break;
      case "rightbottom":
        this.center.set(0, 0);
        this.element.style.setProperty("--mr", "0px");
        this.element.style.setProperty("--mb", "0px");
        this.element.style.setProperty("--ml", `${this.offset.x}px`);
        this.element.style.setProperty("--mt", `${this.offset.y}px`);
        break;
    }
    return position;
  }

  /**
   * 移出
   */
  hide() {
    this.visible = false;
    this.point = null;
    this.userData = {};
    this.lastIndex = null;
  }

  /*
   *距离远近隐藏
   */
  distanceShow(show: boolean) {
    this.element.style.setProperty(
      "--animation",
      show ? "label-enter" : "label-leave"
    );
  }

  /**
   * 坐标更新
   * @param position 坐标
   */
  updatePosition(position: [number, number, number]) {
    this.position.set(...position);
  }
}
/**hover标签 */
const hoverLabel = new KsgLabel();
/**focus标签 */
const focusLabel = new KsgLabel();
export { hoverLabel, focusLabel };
