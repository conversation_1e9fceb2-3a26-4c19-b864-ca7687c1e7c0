import ctx from "../ctx/index";
import {
  Vector2,
  Object3D,
  PerspectiveCamera,
  Raycaster,
  Group,
  Vector3,
  Color,
} from "three";
import {
  GraphType,
  POINT_STATUS,
  STATUS,
  POINT_STUDY_STATUS,
  POINT_STUDY_STATUS_COLOR,
} from "../enums";

// import { Line2 } from "three/examples/jsm/lines/Line2";
// import { LineGeometry } from "three/examples/jsm/lines/LineGeometry";
// import { LineMaterial } from "three/examples/jsm/lines/LineMaterial";
import KsgGraph from "../core/KsgGraph";
/**
 * @param value 添加单位
 * @returns
 */
export function addUnit(value: string | number = 100) {
  if (typeof value === "string" && /px|%|rem|em$/.test(value)) return value;
  return value + "px";
}

/**
 * 根据小球的学习状态获取颜色值并转换为RGB数组
 */
export function pointStatusToColor(pointStatus: POINT_STUDY_STATUS) {
  switch (pointStatus) {
    case POINT_STUDY_STATUS.mastered:
      return hexToRgb(POINT_STUDY_STATUS_COLOR.mastered.toString(16));
    case POINT_STUDY_STATUS.studied:
      return hexToRgb(POINT_STUDY_STATUS_COLOR.studied.toString(16));
    case POINT_STUDY_STATUS.unstudy:
      return hexToRgb(POINT_STUDY_STATUS_COLOR.unstudy.toString(16));
  }
}
function hexToRgb(hex: string): [number, number, number] {
  const sliceArr = [
    hex.substring(0, 2),
    hex.substring(2, 4),
    hex.substring(4, 6),
  ];
  return sliceArr.map((item) => parseInt("0x" + item) / 255) as [
    number,
    number,
    number
  ];
}

/**控制器相关辅助函数 */

export function getClickArea(
  event: MouseEvent,
  dom: HTMLCanvasElement,
  subareas: Object3D,
  camera: PerspectiveCamera
) {
  const x =
    ((event.clientX - dom.getBoundingClientRect().left) / dom.offsetWidth) * 2 -
    1;
  const y =
    -((event.clientY - dom.getBoundingClientRect().top) / dom.offsetHeight) *
      2 +
    1;
  const raycaster = new Raycaster();
  raycaster.setFromCamera(new Vector2(x, y), camera);
  const intersects = raycaster.intersectObjects(subareas.children, true);
  if (intersects.length > 0) {
    let obj: Object3D | null = intersects[0].object;
    while (obj && obj.uuid != subareas.uuid) {
      if (obj.userData.id) {
        return obj.userData.id;
      }
      obj = obj.parent;
    }
  }
  return null;
}

export function map(v: number, i1: number, i2: number, o1: number, o2: number) {
  return o1 + ((o2 - o1) * (v - i1)) / (i2 - i1);
}

export function constrain(v: number, min: number, max: number) {
  if (v < min) v = min;
  else if (v > max) v = max;
  return v;
}

/**
 * 将一个数值从一个范围映射到另一个范围。
 *
 * 这个函数使用线性插值将输入值 `val` 从 `from` 数组定义的范围映射到 `to` 数组定义的范围。`from` 和 `to` 数组必须具有相同的长度，并且至少包含两个元素。`from` 必须单增
 */
export function cmap(val: number, from: number[], to: number[]): number {
  if (from.length !== to.length) {
    throw new Error("The 'from' and 'to' arrays must have the same length.");
  }

  if (from.length < 2) {
    throw new Error(
      "The 'from' and 'to' arrays must contain at least two elements for interpolation."
    );
  }

  // Find the appropriate segment in the 'from' array
  for (let i = 0; i < from.length - 1; i++) {
    if (val >= from[i] && val <= from[i + 1]) {
      // Perform linear interpolation
      const t = (val - from[i]) / (from[i + 1] - from[i]);
      return to[i] + t * (to[i + 1] - to[i]);
    }
  }

  if (val < from[0]) {
    return to[0];
  } else {
    return to[to.length - 1];
  }
}

export function equiv(a: number, b: number) {
  return Math.abs(a - b) < 0.0001;
}

export function setNestedProperty(obj: any, path: string, value: any) {
  const keys = path.split(".");
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    if (!current[keys[i]]) {
      current[keys[i]] = {}; // 创建不存在的路径
    }
    current = current[keys[i]];
  }

  current[keys[keys.length - 1]] = value; // 设置最终属性值
}

/**
 *生成的event函数
 */
export function createHoverPointEventFun(
  el: HTMLElement,
  camera: PerspectiveCamera,
  viewGroup: Group,
  enterCallback: (data: any) => void,
  leaveCallback: (data: any) => void
) {
  let width: number | null = el.getBoundingClientRect().width;
  let height: number | null = el.getBoundingClientRect().height;
  let raycaster: null | Raycaster = new Raycaster();

  let status: STATUS = STATUS.leave;
  let crtObjectId: number | null = null;
  //清空闭包造成的内存泄漏
  function clear() {
    width = null;
    height = null;
    raycaster = null;
  }
  //生成的event事件
  function event(e: MouseEvent) {
    const { offsetX, offsetY } = e;
    raycaster!.setFromCamera(
      new Vector2((offsetX / width!) * 2 - 1, -(offsetY / height!) * 2 + 1),
      camera
    );
    const intersects = raycaster!.intersectObjects(
      [...viewGroup?.children!],
      true
    );
    if (!intersects.length && status === STATUS.enter) {
      leaveCallback(null);
      crtObjectId = null;
      status = STATUS.leave;
      document.body.style.cursor = "";
      return;
    } else if (!intersects.length) return;
    // 距离限制
    if (intersects[0].distance > (ctx.maxDistance ?? 70)) return;
    // 找到第一个知识节点
    for (let i = 0; i < intersects.length; i++) {
      if (intersects[i].object.userData.type === GraphType.Point) {
        intersects[0] = intersects[i];
        break;
      }
    }
    if (intersects[0].object.userData.isAnimation) return;
    if (
      intersects[0].object.userData.type === GraphType.Point &&
      crtObjectId != intersects[0].object.id &&
      !intersects[0].object.children.length &&
      (intersects[0].object.parent as any).status !== POINT_STATUS.focus
    ) {
      enterCallback(intersects[0].object.parent);
      document.body.style.cursor = "pointer";
      crtObjectId = intersects[0].object.id;
      status = STATUS.enter;
    } else if (intersects[0].object.id !== crtObjectId) {
      leaveCallback(intersects[0].object.parent);
      document.body.style.cursor = "";
      crtObjectId = intersects[0].object.id;
      status = STATUS.leave;
    }
  }
  return {
    clear,
    event,
  };
}

/**
 * 获取图中最大的y值
 * @param {KsgGraph} graph 图数据结构
 * @param {number} levelSpace 层级间距
 * @returns {number} 最大的y值
 */
export function getMaxY(graph: KsgGraph, levelSpace: number) {
  return Math.abs(graph.getMaxLevel() * levelSpace);
}

/**
 * 获取该坐标下的dom元素
 * @param {MouseEvent} e 事件对象
 * @returns {HTMLElement} dom元素
 */
export function getDomElement(e: MouseEvent) {
  const { clientX, clientY } = e as MouseEvent;
  return document.elementFromPoint(clientX, clientY) as HTMLElement;
}

/**
 *
 * @param {Node} ele DOM元素
 * @returns 绑定的节点id
 */
export function findValidateParentNode(ele: Node | null) {
  if (!ele) return null;
  if (ele.nodeName === "DIV" && (ele as Element).className === "css2d-label")
    return (ele as Element).getAttribute("id");
  return findValidateParentNode(ele.parentNode);
}

/**
 * 计算未挂载标题label标签的宽高
 * @param {Element} el 需要进行计算的节点
 * @returns {{width:number,hight:number}} [宽,高]
 */
export function computedWH(el: Element): { width: number; height: number } {
  const size: { width: number; height: number } = {
    width: 0,
    height: 0,
  };
  document.body.appendChild(el);
  size.width = el.getBoundingClientRect().width;
  size.height = el.getBoundingClientRect().height;
  document.body.removeChild(el);
  return size;
}

/**
 *计算场景中的模型在屏幕视口中的像素坐标位置
 * @param {Object3D} object 需要计算的对象
 * @param {number} rendererW 渲染器宽
 * @param {number} rendererH 渲染器高
 * @returns {{x:number,y:number}} x,y坐标(相对视口位置)
 */
export function getObjectPosition(
  object: Object3D,
  camera: PerspectiveCamera,
  rendererW: number,
  rendererH: number
): { x: number; y: number } {
  if (!object) return { x: 0, y: 0 };
  const wordPosition = new Vector3();
  object.getWorldPosition(wordPosition);

  wordPosition.project(camera); //转化为DNC（标准化设备坐标，可以理解为屏幕像素位置）坐标，注意x-[-1,1](左到右) y-[-1,1](下到上)
  return {
    x: (wordPosition.x * 0.5 + 0.5) * rendererW, //变为0-1
    y: (0.5 - wordPosition.y * 0.5) * rendererH, //变为0-1
  };
}

/**
 * 知识点掌握状态所对应的颜色
 */
export function studyStatusToColor(studyStatus: POINT_STUDY_STATUS) {
  switch (studyStatus) {
    case POINT_STUDY_STATUS.unstudy:
      return new Color(POINT_STUDY_STATUS_COLOR.unstudy);
    case POINT_STUDY_STATUS.studied:
      return new Color(POINT_STUDY_STATUS_COLOR.studied);
    case POINT_STUDY_STATUS.mastered:
      return new Color(POINT_STUDY_STATUS_COLOR.mastered);
  }
}
