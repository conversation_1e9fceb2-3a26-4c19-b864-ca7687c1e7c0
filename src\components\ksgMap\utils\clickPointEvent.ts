import { Ray<PERSON>, Vector2, <PERSON><PERSON><PERSON><PERSON> } from "three";
import type { Point, Size } from "../types";
import KsgPoint from "../core/KsgPoints";
import { type Context } from "../ctx";
export default function createClickEvent(
  elSize: Size,
  ctx: Partial<Context>,
  clickedPointCallback: (data: Point) => void
) {
  let width: number | null = elSize.width;
  let height: number | null = elSize.height;
  let raycaster: null | Raycaster = new Raycaster();

  //清空闭包造成的内存泄漏
  function clear() {
    width = null;
    height = null;
    raycaster = null;
  }
  // 更新尺寸
  function updateSize(w: number, h: number) {
    width = w;
    height = h;
  }
  //生成的event事件
  function event(e: MouseEvent) {
    if (!ctx.pointsMesh) return;
    const { offsetX, clientX, offsetY, clientY, button } = e;
    if (isClickedLabel(clientX, clientY) || button !== MOUSE.LEFT) return;
    raycaster!.setFromCamera(
      new Vector2((offsetX / width!) * 2 - 1, -(offsetY / height!) * 2 + 1),
      ctx.camera!
    );
    const intersects = raycaster!.intersectObject(ctx.pointsMesh);

    let fistPointIndex: number | null = null;
    if (intersects.length > 0) {
      fistPointIndex = intersects[0].index as number;
    }
    if (intersects[0]?.object && intersects[0].distance <= ctx.maxDistance!) {
      const id = (intersects[0].object as KsgPoint).getPointData(
        fistPointIndex!
      )!.id;
      clickedPointCallback(
        // (intersects[0].object as KsgPoint).getPointData(fistPointIndex!)
        ctx.graph?.getPointById(id)!
      );
    }
  }
  return {
    clear,
    event,
    updateSize,
  };
}

/**
 * 辅助点击目标节点是否点击到label标签
 * @param {clientX} clientX 鼠标点击的x坐标
 * @param {clientY} 鼠标点击的y坐标
 * @return {boolean} true:点击到label标签 false:点击到其他地方
 */
function isClickedLabel(clientX: number, clientY: number): boolean {
  const e = document.elementFromPoint(clientX, clientY);
  const id = findValidateParentNode(e);
  if (id) return true;
  return false;
}

function findValidateParentNode(ele: Node | null) {
  if (!ele) return null;
  if (ele.nodeName === "DIV" && (ele as Element).className === "css2d-label")
    return (ele as Element).getAttribute("id");
  return findValidateParentNode(ele.parentNode);
}
