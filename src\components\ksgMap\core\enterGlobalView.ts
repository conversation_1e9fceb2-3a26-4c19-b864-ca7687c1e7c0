import ctx from "../ctx";
import { enterGlobalAnimation } from "../animation/enterGlobal";
import { lineLeaveAnimation } from "../animation/load";
import { VIEW_MODE } from "../enums";
import TWEEN from "@tweenjs/tween.js";
import focusCrust from "./focusCrust";
/**
 * 处理全局所有节点的状态和视角切换
 * @param {[number, number, number]} to 视角移动的目的坐标
 */
export default function enterGlobalView(to: [number, number, number]) {
  TWEEN.removeAll();
  //切换模式
  if (ctx.viewMode == VIEW_MODE.GLOBAL_VIEW) return;
  ctx.viewMode = VIEW_MODE.GLOBAL_VIEW;
  focusCrust.hide();
  ctx.pointsMesh!.breathAnimationSwitch();
  ctx.focusStack?.push("null");
  if (ctx.focusLine) {
    ctx.focusLine.dispose();
    lineLeaveAnimation(ctx.focusLine).then(() => {
      ctx.viewGroup!.remove(ctx.focusLine!);
    });
  }
  // 进入全局视角
  return enterGlobalAnimation(ctx.controls!, to);
}
