/**
 * 分帧处理调度器
 */
export default class FrameScheduler {
  private taskQueue: (() => boolean)[] = []; //函数返回true表示最后一个任务执行完成，false表示继续执行
  private isRunning = false;
  completedCallback: () => void = () => {};
  /**
   *添加任务
   * @param task 任务
   */
  addTask(task: () => boolean) {
    this.taskQueue.push(task);
    if (!this.isRunning) {
      this.runNextTask();
    }
  }

  /**
   * 分帧计算函数，此函数在渲染帧中调用，调用一次择执行一次处理任务
   */
  runNextTask() {
    this.isRunning = true;
    requestAnimationFrame(() => {
      const task = this.taskQueue.shift();
      if (task) {
        const isCompleted = task();
        if (isCompleted) {
          this.isRunning = false;
          this.completedCallback();
          return;
        } //执行完成触发回调
        this.runNextTask();
      } else {
        this.isRunning = false;
      }
    });
  }
  /**
   * 清空任务
   */
  clearTasks() {
    this.taskQueue = [];
    this.isRunning = false;
  }

  /**
   *执行完成
   */
  onCompleted(callback: () => void) {
    if (!callback) return;
    this.completedCallback = callback;
  }
}
