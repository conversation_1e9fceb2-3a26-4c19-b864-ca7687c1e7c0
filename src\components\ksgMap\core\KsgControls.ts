import {
  EventDispatcher,
  PerspectiveCamera,
  MOUSE,
  Quaternion,
  Spherical,
  TOUCH,
  Vector2,
  Vector3,
  type BaseEvent,
  Matrix4,
  Group,
  Object3D,
  Vector4,
} from "three";
import { cmap } from "../utils";

enum KsgMode {
  /** 知识点层 */
  Star,
  /** 领域层 */
  Domain,
}

const _startEvent = { type: "start" } as BaseEvent;
const _endEvent = { type: "end" } as BaseEvent;
const _changeEvent = { type: "change" } as BaseEvent;

class KsgControls extends EventDispatcher<{ [key: string]: BaseEvent }> {
  /** 相机 */
  object: PerspectiveCamera;
  /** 根领域 */
  rootAreaObj: Group;
  subareas: Object3D | null;
  /** The HTML element used for event listeners */
  domElement: HTMLElement;
  /** 是否启用 */
  enabled: boolean;
  /** 相机焦点 */
  target: Vector3;
  /** 相机距离焦点的最小距离 */
  minDistance: number;
  /** 相机距离焦点的最大距离 */
  maxDistance: number;
  /** phi 最小角度 */
  minPolarAngle: number;
  /** phi 最大角度 */
  maxPolarAngle: number;
  /** 启用阻尼 */
  enableDamping: boolean;
  /** 阻尼系数 */
  dampingFactor: number;
  /** 是否启用缩放 */
  enableZoom: boolean;
  /** 缩放速度 */
  zoomSpeed: number;
  /** 是否启用旋转 */
  enableRotate: boolean;
  /** 旋转速度 */
  rotateSpeed: number;
  /** 是否启用平移 */
  enablePan: boolean;
  /** 平移速度 */
  panSpeed: number;
  /** 当前模式 */
  mode: KsgMode;
  /** 键盘按键 */
  keys: { LEFT: string; UP: string; RIGHT: string; BOTTOM: string };
  /** 鼠标按键 */
  mouseButtons: { LEFT: MOUSE; MIDDLE: MOUSE; RIGHT: MOUSE };
  /** 触摸事件 */
  touches: { ONE: TOUCH; TWO: TOUCH };
  /** pixels moved per arrow key push */
  keyPanSpeed: number;
  /** camera y 轴最小范围 */
  yMinRange: number;
  /** camera y 轴最大范围 */
  yMaxRange: number;
  /** target y 轴范围为 [yMinRange, yMaxRange - yDelta] */
  yDelta: number;
  /** 相机焦点 y 轴 */
  yAxis: Vector3;

  /**自动旋转 */
  autoRotate: boolean;
  /** 自动旋转速度 */
  autoRotateSpeed: number;
  /**是否在控制状态中 */
  isControls: boolean;
  controlsTimer: number | NodeJS.Timeout | null;

  _domElementKeyEvents: HTMLElement | null;
  update: (deltaTime?: number | null) => boolean;
  dispose: () => void;
  changeMode: (mode: KsgMode) => void;

  constructor(
    object: PerspectiveCamera,
    rootArea: Group,
    domElement: HTMLElement
  ) {
    super();
    this.object = object;
    this.rootAreaObj = rootArea;
    this.subareas = null;
    this.domElement = domElement;
    this.domElement.style.touchAction = "none"; // disable touch scroll

    this.enabled = true;

    this.target = new Vector3();
    this.minDistance = 0;
    this.maxDistance = Infinity;
    this.minPolarAngle = 0;
    this.maxPolarAngle = Math.PI;
    this.enableDamping = false;
    this.dampingFactor = 0.05;
    this.enableZoom = true;
    this.zoomSpeed = 0.4;
    this.enableRotate = true;
    this.rotateSpeed = 1.0;
    this.enablePan = true;
    this.panSpeed = 1.0;
    this.mode = KsgMode.Star;
    this.keys = {
      LEFT: "ArrowLeft",
      UP: "ArrowUp",
      RIGHT: "ArrowRight",
      BOTTOM: "ArrowDown",
    };
    this.mouseButtons = {
      LEFT: MOUSE.ROTATE,
      MIDDLE: MOUSE.DOLLY,
      RIGHT: MOUSE.PAN,
    };
    this.touches = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN };
    this.keyPanSpeed = 7.0;
    this.yMinRange = -100;
    this.yMaxRange = 100;
    this.yDelta = 100;
    this.yAxis = new Vector3(0, 0, 0);
    this._domElementKeyEvents = null;

    this.autoRotate = false;
    this.autoRotateSpeed = 2.0;
    this.isControls = false;
    this.controlsTimer = null;

    this.update = (function () {
      const offset = new Vector3();

      // so camera.up is the orbit axis
      const quat = new Quaternion().setFromUnitVectors(
        object.up,
        new Vector3(0, 1, 0)
      );
      const quatInverse = quat.clone().invert();
      const lastPosition = new Vector3();
      const lastQuaternion = new Quaternion();
      const lastTargetPosition = new Vector3();
      const twoPI = 2 * Math.PI;

      return function update(deltaTime: number | null = null) {
        const position = scope.object.position;
        offset.copy(position).sub(scope.target);

        // rotate offset to "y-axis-is-up" space
        offset.applyQuaternion(quat);

        // angle from z-axis around y-axis
        spherical.setFromVector3(offset);
        switch (scope.mode) {
          case KsgMode.Star:
            if (scope.enableDamping) {
              spherical.theta += sphericalDelta.theta * scope.dampingFactor;
              spherical.phi += sphericalDelta.phi * scope.dampingFactor;
            } else {
              spherical.theta += sphericalDelta.theta;
              spherical.phi += sphericalDelta.phi;
            }
            break;
          case KsgMode.Domain:
            break;
        }

        // restrict phi to be between desired limits
        spherical.phi = Math.max(
          scope.minPolarAngle,
          Math.min(scope.maxPolarAngle, spherical.phi)
        );
        spherical.makeSafe();

        // move target to panned location
        switch (scope.mode) {
          case KsgMode.Star:
            panOffset.setX(0);
            panOffset.setZ(0);
            if (scope.enableDamping === true) {
              scope.target.addScaledVector(panOffset, scope.dampingFactor);
            } else {
              scope.target.add(panOffset);
            }
            // 限制 target 在 y 轴上的范围
            scope.target.y = Math.max(scope.target.y, scope.yMinRange);
            scope.target.y = Math.min(
              scope.target.y,
              scope.yMaxRange - scope.yDelta
            );
            break;
          case KsgMode.Domain:
            if (scope.enableDamping === true) {
              scope.rootAreaObj.updateWorldMatrix(true, false);
              //@ts-ignore
              const offset = new Vector4(...panOffset, 0)
                .multiplyScalar(scope.dampingFactor)
                .applyMatrix4(scope.rootAreaObj.matrixWorld.clone().invert());
              scope.subareas?.position.add(offset);
            } else {
              scope.rootAreaObj.updateWorldMatrix(true, false);
              //@ts-ignore

              const offset = new Vector4(...panOffset, 0).applyMatrix4(
                scope.rootAreaObj.matrixWorld.clone().invert()
              );
              scope.subareas?.position.add(offset);
            }
            break;
        }

        let zoomChanged = false;
        const prevRadius = spherical.radius;
        spherical.radius = clampDistance(spherical.radius * scale);
        zoomChanged = prevRadius != spherical.radius;

        offset.setFromSpherical(spherical);

        // rotate offset back to "camera-up-vector-is-up" space
        offset.applyQuaternion(quatInverse);

        switch (scope.mode) {
          case KsgMode.Star:
            position.copy(scope.target).add(offset);
            scope.object.lookAt(scope.target);
            break;
          case KsgMode.Domain:
            offset.setLength(cmap(dollyOffset, [-200, 200], [-1, 1]));
            scope.rootAreaObj.position.add(offset.negate());
            // scope.object.lookAt(scope.target);
            break;
        }

        if (scope.enableDamping === true) {
          dollyOffset *= 1 - scope.dampingFactor;
          sphericalDelta.theta *= 1 - scope.dampingFactor;
          sphericalDelta.phi *= 1 - scope.dampingFactor;
          panOffset.multiplyScalar(1 - scope.dampingFactor);
        } else {
          dollyOffset = 0;
          sphericalDelta.set(0, 0, 0);
          panOffset.set(0, 0, 0);
        }

        scale = 1;
        // update condition is:
        // min(camera displacement, camera rotation in radians)^2 > EPS
        // using small-angle approximation cos(x/2) = 1 - x^2 / 8

        // 限制相机 y 轴位置
        switch (scope.mode) {
          case KsgMode.Star:
            scope.object.position.y = Math.min(
              Math.max(scope.object.position.y, scope.yMinRange),
              scope.yMaxRange
            );
            break;
          case KsgMode.Domain:
            break;
        }

        if (
          zoomChanged ||
          lastPosition.distanceToSquared(scope.object.position) > EPS ||
          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS ||
          lastTargetPosition.distanceToSquared(scope.target) > EPS
        ) {
          scope.dispatchEvent(_changeEvent);
          lastPosition.copy(scope.object.position);
          lastQuaternion.copy(scope.object.quaternion);
          lastTargetPosition.copy(scope.target);
          return true;
        }
        return false;
      };
    })();

    this.dispose = function () {
      scope.domElement.removeEventListener("contextmenu", onContextMenu);
      scope.domElement.removeEventListener("pointerdown", onPointerDown);
      scope.domElement.removeEventListener("pointercancel", onPointerUp);
      scope.domElement.removeEventListener("wheel", onMouseWheel);
      scope.domElement.removeEventListener("pointermove", onPointerMove);
      scope.domElement.removeEventListener("pointerup", onPointerUp);
      const document = scope.domElement.getRootNode(); // offscreen canvas compatibility
      document.removeEventListener("keydown", interceptControlDown, {
        capture: true,
      });
      if (scope._domElementKeyEvents !== null) {
        scope._domElementKeyEvents.removeEventListener("keydown", onKeyDown);
        scope._domElementKeyEvents = null;
      }
    };

    this.changeMode = function (mode) {
      scope.mode = mode;
      sphericalDelta.set(0, 0, 0);
      panOffset.set(0, 0, 0);
      dollyOffset = 0;
    };

    const scope = this;
    const STATE = {
      NONE: -1,
      ROTATE: 0,
      DOLLY: 1,
      PAN: 2,
      TOUCH_ROTATE: 3,
      TOUCH_PAN: 4,
      TOUCH_DOLLY_PAN: 5,
      TOUCH_DOLLY_ROTATE: 6,
    };

    let state = STATE.NONE;
    let scale = 1;
    let controlActive = false;
    let dollyOffset = 0;
    const EPS = 0.000001;
    const panOffset = new Vector3();
    const rotateStart = new Vector2();
    const rotateEnd = new Vector2();
    const rotateDelta = new Vector2();
    const panStart = new Vector2();
    const panEnd = new Vector2();
    const panDelta = new Vector2();
    const dollyStart = new Vector2();
    const dollyEnd = new Vector2();
    const dollyDelta = new Vector2();
    const dollyDirection = new Vector3();

    // current position in spherical coordinates
    const spherical = new Spherical();
    const sphericalDelta = new Spherical();

    const pointers: any[] = [];
    const pointerPositions: { [key: number]: Vector2 } = {};

    function getZoomScale(delta: number) {
      const normalizedDelta = Math.abs(delta * 0.01);
      return Math.pow(0.95, scope.zoomSpeed * normalizedDelta);
    }

    function rotateLeft(angle: number) {
      sphericalDelta.theta -= angle;
    }

    function rotateUp(angle: number) {
      sphericalDelta.phi -= angle;
    }

    const panLeft = (function () {
      const v = new Vector3();
      return function panLeft(distance: number, objectMatrix: Matrix4) {
        v.setFromMatrixColumn(objectMatrix, 0); // get X column of objectMatrix
        v.multiplyScalar(-distance);
        panOffset.add(v);
      };
    })();

    const panUp = (function () {
      const v = new Vector3();
      return function panUp(distance: number, objectMatrix: Matrix4) {
        v.setFromMatrixColumn(objectMatrix, 1);
        v.multiplyScalar(distance);
        panOffset.add(v);
      };
    })();

    // deltaX and deltaY are in pixels; right and down are positive
    const pan = (function () {
      const offset = new Vector3();
      return function pan(deltaX: number, deltaY: number) {
        const element = scope.domElement;
        const position = scope.object.position;
        switch (scope.mode) {
          case KsgMode.Star:
            offset.copy(position).sub(scope.target);
            break;
          case KsgMode.Domain:
            deltaX = -deltaX;
            deltaY = -deltaY;
            offset.copy(position).sub(scope.rootAreaObj.position);
            break;
        }

        let targetDistance = offset.length();
        // half of the fov is center to top of screen
        targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0);
        // we use only clientHeight here so aspect ratio does not distort speed
        panLeft(
          (2 * deltaX * targetDistance) / element.clientHeight,
          scope.object.matrix
        );
        panUp(
          (2 * deltaY * targetDistance) / element.clientHeight,
          scope.object.matrix
        );
      };
    })();

    function dollyOut(dollyScale: number) {
      scale /= dollyScale;
    }

    function dollyIn(dollyScale: number) {
      scale *= dollyScale;
    }

    function clampDistance(dist: number) {
      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist));
    }

    function handleMouseDownRotate(event: MouseEvent) {
      rotateStart.set(event.clientX, event.clientY);
    }

    function handleMouseDownDolly(event: MouseEvent) {
      dollyStart.set(event.clientX, event.clientY);
    }

    function handleMouseDownPan(event: MouseEvent) {
      panStart.set(event.clientX, event.clientY);
    }

    function handleMouseMoveRotate(event: MouseEvent) {
      rotateEnd.set(event.clientX, event.clientY);
      rotateDelta
        .subVectors(rotateEnd, rotateStart)
        .multiplyScalar(scope.rotateSpeed);
      const element = scope.domElement;
      rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight); // yes, height
      rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight);
      rotateStart.copy(rotateEnd);
      scope.update();
    }

    function handleMouseMoveDolly(event: MouseEvent) {
      dollyEnd.set(event.clientX, event.clientY);
      dollyDelta.subVectors(dollyEnd, dollyStart);
      if (dollyDelta.y > 0) {
        dollyOut(getZoomScale(dollyDelta.y));
      } else if (dollyDelta.y < 0) {
        dollyIn(getZoomScale(dollyDelta.y));
      }
      dollyOffset += dollyDelta.y;
      dollyStart.copy(dollyEnd);
      scope.update();
    }

    function handleMouseMovePan(event: MouseEvent) {
      panEnd.set(event.clientX, event.clientY);
      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);
      pan(panDelta.x, panDelta.y);
      panStart.copy(panEnd);
      scope.update();
    }

    function handleMouseWheel(event: any) {
      if (event.deltaY < 0) {
        dollyIn(getZoomScale(event.deltaY));
      } else if (event.deltaY > 0) {
        dollyOut(getZoomScale(event.deltaY));
      }
      dollyOffset += event.deltaY;
      scope.update();
    }

    function handleKeyDown(event: {
      code: any;
      ctrlKey: any;
      metaKey: any;
      shiftKey: any;
      preventDefault: () => void;
    }) {
      let needsUpdate = false;

      switch (event.code) {
        case scope.keys.UP:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateUp(
              (2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            pan(0, scope.keyPanSpeed);
          }

          needsUpdate = true;
          break;

        case scope.keys.BOTTOM:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateUp(
              (-2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            pan(0, -scope.keyPanSpeed);
          }

          needsUpdate = true;
          break;

        case scope.keys.LEFT:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateLeft(
              (2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            pan(scope.keyPanSpeed, 0);
          }

          needsUpdate = true;
          break;

        case scope.keys.RIGHT:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateLeft(
              (-2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            pan(-scope.keyPanSpeed, 0);
          }

          needsUpdate = true;
          break;
      }

      if (needsUpdate) {
        // prevent the browser from scrolling on cursor keys
        event.preventDefault();

        scope.update();
      }
    }

    function handleTouchStartRotate(event: PointerEvent) {
      if (pointers.length === 1) {
        rotateStart.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        rotateStart.set(x, y);
      }
    }

    function handleTouchStartPan(event: PointerEvent) {
      if (pointers.length === 1) {
        panStart.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        panStart.set(x, y);
      }
    }

    function handleTouchStartDolly(event: PointerEvent) {
      const position = getSecondPointerPosition(event);

      const dx = event.pageX - position.x;
      const dy = event.pageY - position.y;

      const distance = Math.sqrt(dx * dx + dy * dy);

      dollyStart.set(0, distance);
    }

    function handleTouchStartDollyPan(event: any) {
      if (scope.enableZoom) handleTouchStartDolly(event);

      if (scope.enablePan) handleTouchStartPan(event);
    }

    function handleTouchStartDollyRotate(event: any) {
      if (scope.enableZoom) handleTouchStartDolly(event);

      if (scope.enableRotate) handleTouchStartRotate(event);
    }

    function handleTouchMoveRotate(event: PointerEvent) {
      if (pointers.length == 1) {
        rotateEnd.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        rotateEnd.set(x, y);
      }

      rotateDelta
        .subVectors(rotateEnd, rotateStart)
        .multiplyScalar(scope.rotateSpeed);

      const element = scope.domElement;

      rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight); // yes, height

      rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight);

      rotateStart.copy(rotateEnd);
    }

    function handleTouchMovePan(event: PointerEvent) {
      if (pointers.length === 1) {
        panEnd.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        panEnd.set(x, y);
      }

      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);

      pan(panDelta.x, panDelta.y);

      panStart.copy(panEnd);
    }

    function handleTouchMoveDolly(event: PointerEvent) {
      const position = getSecondPointerPosition(event);

      const dx = event.pageX - position.x;
      const dy = event.pageY - position.y;

      const distance = Math.sqrt(dx * dx + dy * dy);

      dollyEnd.set(0, distance);

      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed));

      dollyOut(dollyDelta.y);

      dollyStart.copy(dollyEnd);

      const centerX = (event.pageX + position.x) * 0.5;
      const centerY = (event.pageY + position.y) * 0.5;
    }

    function handleTouchMoveDollyPan(event: PointerEvent) {
      if (scope.enableZoom) handleTouchMoveDolly(event);
      if (scope.enablePan) handleTouchMovePan(event);
    }

    function handleTouchMoveDollyRotate(event: PointerEvent) {
      if (scope.enableZoom) handleTouchMoveDolly(event);
      if (scope.enableRotate) handleTouchMoveRotate(event);
    }

    function onPointerDown(event: PointerEvent) {
      if (scope.enabled === false) return;
      if (pointers.length === 0) {
        scope.domElement.setPointerCapture(event.pointerId);
        scope.domElement.addEventListener("pointermove", onPointerMove);
        scope.domElement.addEventListener("pointerup", onPointerUp);
      }

      if (isTrackingPointer(event)) return;

      addPointer(event);

      if (event.pointerType === "touch") {
        onTouchStart(event);
      } else {
        onMouseDown(event);
      }
    }

    function onPointerMove(event: PointerEvent) {
      if (scope.enabled === false) return;
      changeControlsStatus();
      if (event.pointerType === "touch") {
        onTouchMove(event);
      } else {
        onMouseMove(event);
      }
    }

    /**
     * 修改控制状态
     */
    function changeControlsStatus() {
      if (scope.controlsTimer) clearTimeout(scope.controlsTimer as number);
      scope.isControls = true;
      scope.controlsTimer = setTimeout(() => {
        scope.isControls = false;
      }, 300);
    }

    function onPointerUp(event: PointerEvent) {
      removePointer(event);
      switch (pointers.length) {
        case 0:
          scope.domElement.releasePointerCapture(event.pointerId);
          scope.domElement.removeEventListener("pointermove", onPointerMove);
          scope.domElement.removeEventListener("pointerup", onPointerUp);
          scope.dispatchEvent(_endEvent);
          state = STATE.NONE;
          break;
        case 1:
          const pointerId = pointers[0];
          const position = pointerPositions[pointerId];
          // minimal placeholder event - allows state correction on pointer-up
          onTouchStart({
            pointerId: pointerId,
            pageX: position.x,
            pageY: position.y,
          } as PointerEvent);
          break;
      }
    }

    function onMouseDown(event: MouseEvent) {
      let mouseAction;
      switch (event.button) {
        case 0:
          mouseAction = scope.mouseButtons.LEFT;
          break;
        case 1:
          mouseAction = scope.mouseButtons.MIDDLE;
          break;
        case 2:
          mouseAction = scope.mouseButtons.RIGHT;
          break;
        default:
          mouseAction = -1;
      }

      switch (mouseAction) {
        case MOUSE.DOLLY:
          if (scope.enableZoom === false) return;
          handleMouseDownDolly(event);
          state = STATE.DOLLY;
          break;
        case MOUSE.ROTATE:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            if (scope.enablePan === false) return;
            handleMouseDownPan(event);
            state = STATE.PAN;
          } else {
            if (scope.enableRotate === false) return;
            handleMouseDownRotate(event);
            state = STATE.ROTATE;
          }
          break;
        case MOUSE.PAN:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            if (scope.enableRotate === false) return;
            handleMouseDownRotate(event);
            state = STATE.ROTATE;
          } else {
            if (scope.enablePan === false) return;
            handleMouseDownPan(event);
            state = STATE.PAN;
          }
          break;
        default:
          state = STATE.NONE;
      }
      if (state !== STATE.NONE) {
        scope.dispatchEvent(_startEvent);
      }
    }

    function onMouseMove(event: MouseEvent) {
      switch (state) {
        case STATE.ROTATE:
          if (scope.enableRotate === false) return;
          handleMouseMoveRotate(event);
          break;
        case STATE.DOLLY:
          if (scope.enableZoom === false) return;
          handleMouseMoveDolly(event);
          break;
        case STATE.PAN:
          if (scope.enablePan === false) return;
          handleMouseMovePan(event);
          break;
      }
    }

    function onMouseWheel(event: WheelEvent) {
      if (
        scope.enabled === false ||
        scope.enableZoom === false ||
        state !== STATE.NONE
      )
        return;
      event.preventDefault();
      scope.dispatchEvent(_startEvent);
      handleMouseWheel(customWheelEvent(event));
      scope.dispatchEvent(_endEvent);
    }

    function customWheelEvent(event: WheelEvent) {
      const mode = event.deltaMode;
      // minimal wheel event altered to meet delta-zoom demand
      const newEvent = {
        clientX: event.clientX,
        clientY: event.clientY,
        deltaY: event.deltaY,
      };
      switch (mode) {
        case WheelEvent.DOM_DELTA_LINE: // LINE_MODE
          newEvent.deltaY *= 16;
          break;
        case WheelEvent.DOM_DELTA_PAGE: // PAGE_MODE
          newEvent.deltaY *= 100;
          break;
      }
      // detect if event was triggered by pinching
      if (event.ctrlKey && !controlActive) {
        newEvent.deltaY *= 10;
      }
      return newEvent;
    }

    function interceptControlDown(event: Event) {
      const e = event as KeyboardEvent;
      if (e.key === "Control") {
        controlActive = true;
        const document = scope.domElement.getRootNode(); // offscreen canvas compatibility
        document.addEventListener("keyup", interceptControlUp, {
          passive: true,
          capture: true,
        });
      }
    }

    function interceptControlUp(event: Event) {
      const e = event as KeyboardEvent;
      if (e.key === "Control") {
        controlActive = false;
        const document = scope.domElement.getRootNode(); // offscreen canvas compatibility
        document.removeEventListener("keyup", interceptControlUp, {
          capture: true,
        });
      }
    }

    function onKeyDown(event: KeyboardEvent) {
      if (scope.enabled === false || scope.enablePan === false) return;
      handleKeyDown(event);
    }

    function onTouchStart(event: PointerEvent) {
      trackPointer(event);
      switch (pointers.length) {
        case 1:
          switch (scope.touches.ONE) {
            case TOUCH.ROTATE:
              if (scope.enableRotate === false) return;
              handleTouchStartRotate(event);
              state = STATE.TOUCH_ROTATE;
              break;
            case TOUCH.PAN:
              if (scope.enablePan === false) return;
              handleTouchStartPan(event);
              state = STATE.TOUCH_PAN;
              break;
            default:
              state = STATE.NONE;
          }
          break;
        case 2:
          switch (scope.touches.TWO) {
            case TOUCH.DOLLY_PAN:
              if (scope.enableZoom === false && scope.enablePan === false)
                return;
              handleTouchStartDollyPan(event);
              state = STATE.TOUCH_DOLLY_PAN;
              break;
            case TOUCH.DOLLY_ROTATE:
              if (scope.enableZoom === false && scope.enableRotate === false)
                return;
              handleTouchStartDollyRotate(event);
              state = STATE.TOUCH_DOLLY_ROTATE;
              break;
            default:
              state = STATE.NONE;
          }
          break;
        default:
          state = STATE.NONE;
      }

      if (state !== STATE.NONE) {
        scope.dispatchEvent(_startEvent);
      }
    }

    function onTouchMove(event: PointerEvent) {
      trackPointer(event);

      switch (state) {
        case STATE.TOUCH_ROTATE:
          if (scope.enableRotate === false) return;

          handleTouchMoveRotate(event);

          scope.update();

          break;

        case STATE.TOUCH_PAN:
          if (scope.enablePan === false) return;

          handleTouchMovePan(event);

          scope.update();

          break;

        case STATE.TOUCH_DOLLY_PAN:
          if (scope.enableZoom === false && scope.enablePan === false) return;

          handleTouchMoveDollyPan(event);

          scope.update();

          break;

        case STATE.TOUCH_DOLLY_ROTATE:
          if (scope.enableZoom === false && scope.enableRotate === false)
            return;

          handleTouchMoveDollyRotate(event);

          scope.update();

          break;

        default:
          state = STATE.NONE;
      }
    }

    function onContextMenu(event: MouseEvent) {
      if (scope.enabled === false) return;
      event.preventDefault();
    }

    function addPointer(event: PointerEvent) {
      pointers.push(event.pointerId);
    }

    function removePointer(event: PointerEvent) {
      delete pointerPositions[event.pointerId];
      for (let i = 0; i < pointers.length; i++) {
        if (pointers[i] == event.pointerId) {
          pointers.splice(i, 1);
          return;
        }
      }
    }

    function isTrackingPointer(event: PointerEvent) {
      for (let i = 0; i < pointers.length; i++) {
        if (pointers[i] == event.pointerId) return true;
      }
      return false;
    }

    function trackPointer(event: PointerEvent) {
      let position = pointerPositions[event.pointerId];
      if (position === undefined) {
        position = new Vector2();
        pointerPositions[event.pointerId] = position;
      }
      position.set(event.pageX, event.pageY);
    }

    function getSecondPointerPosition(event: PointerEvent) {
      const pointerId =
        event.pointerId === pointers[0] ? pointers[1] : pointers[0];
      return pointerPositions[pointerId];
    }

    scope.domElement.addEventListener("contextmenu", onContextMenu);
    scope.domElement.addEventListener("pointerdown", onPointerDown);
    scope.domElement.addEventListener("pointercancel", onPointerUp);
    scope.domElement.addEventListener("wheel", onMouseWheel, {
      passive: false,
    });
    const document = scope.domElement.getRootNode(); // offscreen canvas compatibility
    document.addEventListener("keydown", interceptControlDown, {
      passive: true,
      capture: true,
    });
  }
  /**
   * 自动旋转函数,若要开启自动旋转功能,请设置autoRotate为true
   * 渲染帧中调用此方法
   */
  autoRotateUpdate(deltaTime: number) {
    //cameral auto rotate around
    if (this.autoRotate) {
      const offset1 = this.object.position.clone().sub(this.target);
      const angle = this.autoRotateSpeed * deltaTime!;
      const rotationMatrix = new Matrix4().makeRotationY(angle);
      offset1.applyMatrix4(rotationMatrix);
      this.object.position.copy(this.target).add(offset1);
      this.object.lookAt(this.target);
    }
  }
}

export { KsgMode, KsgControls };
