import type {
  CameraConfig,
  RendererConfig,
  SceneConfig,
  ControlsConfig,
} from "../types/index";
import ctx from "../ctx";
import { MODE } from "../enums";
import { MOUSE } from "three";
const defaultConfig: any = {
  model: MODE.Single_ROOT,
  viewRange: {
    minX: 0,
    maxX: 0,
    minY: 0,
    maxY: 0,
  },
  camera: {
    fov: 45,
    // aspect: 1.778, //在初始化的时候需要根据画布尺寸配置，默认900：506
    aspect: 1200 / 675, //在初始化的时候需要根据画布尺寸配置，默认900：506
    near: 0.1,
    far: 1000,
    position: {
      x: 30.2,
      y: -3.14,
      z: 24.98,
    },
  },
  renderer: {
    width: 1200,
    height: 675,
    webGLRenderer: {
      antialias: true,
    },
  },
  scene: {
    backgroundIntensity: 0.02,
    /**场景模糊度 */
    backgroundBlurriness: 0.0,
    /**溯源图整体位置 */
    groupPosition: [0, 6, 0],
  },
  controls: {
    position: [30, -3.14, 24],
    target: [0, 0, 0],
    minPolarAngle: 0.78539,
    maxPolarAngle: 2.35619,
    minDistance: 1,
    maxDistance: 5000,
    mouseButtons: {
      LEFT: MOUSE.ROTATE,
      MIDDLE: MOUSE.DOLLY,
      RIGHT: MOUSE.PAN,
    },
    enableDamping: true,
    /**沿y轴移动范围 */
    yMinRange: -1000,
    yMaxRange: 110,
    yDelta: 100,
  },
  pointsLevelPager: {
    current: 1, // 当前层级，
    levelSize: 4, // 层数
    total: Infinity, //一共多少个节点
  },
  levelSpace: 15,
  pointSpace: 7,
  point: {
    radius: 0.5,
    space: 2,
  },
    /**连线流光效果相关配置*/
  line: {
    //流光长度
    length: 0.4,
    speed: 0.15,
    isRandom: false,
  },
  hoverLabel: {
    offsetX: 15,
    offsetY: 15,
  },
  maxDistance: 110,
  focusBack: () => {},
  focusBackToRoot: () => {},
};
export function useInitThreeJsConfig(option: Options = {}) {
  for (const key of Object.keys(option) as Array<keyof Options>) {
    const value = option[key];
    if (typeof value === "object" && value !== null) {
      // 合并对象属性配置
      Object.assign(defaultConfig[key], value);
    } else {
      (defaultConfig[key] as any) = value;
    }
  }
  // 全局维护
  Object.assign(ctx, {
    ...defaultConfig,
  });

  return {
    cameraConfig: defaultConfig.camera,
    renderConfig: defaultConfig.renderer,
    sceneConfig: defaultConfig.scene,
    controlsConfig: defaultConfig.controls,
    wrapperEleSizeConfig: {
      width: defaultConfig.renderer.width as number,
      height: defaultConfig.renderer.height as number,
    },
  };
}

export type Options = {
  model?: MODE;
  /**视口范围 */
  viewRange?: {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
  };
  //相机配置
  camera?: CameraConfig;
  // 渲染器配置
  renderer?: RendererConfig;
  // scene场景中配置
  scene?: SceneConfig;
  // 控制器配置
  controls?: ControlsConfig;
  // 知识节点查询
  pointsLevelPager?: {
    // 当前层级，
    current: number;
    // 层数
    levelSize: number;
    //总层数
    total?: number;
  };

  /*层级间隔距离*/
  levelSpace?: number;
  /*知识节点相关配置 */
  point?: {
    //节点半径
    radius: number;
    //节点间距
    space: number;
  };
    /**连线流光效果相关配置*/
  line?: {
    //流光长度
    length?: number;
    // 流光移动速度
    speed?: number;
    // 多条连线流光效果是否随机
    isRandom?: boolean;
  };
  /*hover弹窗xy轴偏移配置 */
  hoverLabel?: {
    //x轴偏移量
    offsetX: number;
    //y轴偏移量
    offsetY: number;
  };
  /*弹窗显示j距离限制 */
  maxDistance?: number;
  /*点之间的间距*/
  pointSpace?: number;
  /*回到根节点*/
  focusBackToRoot?: () => void;
  /*子图回退 */
  focusBack?: () => void;
};
