/**
 *
 * @param onMove 移动时触发的事件
 * @param onMoveEnd 移动结束后并且在等待一段时间内没有进行任何操作后触发的事件
 * @param moveEndDelay 移动结束后等待时间
 */
export function createMouseMoveEvent(
  onMove: () => void,
  onMoveEnd: () => void,
  moveEndDelay: number = 100
) {
  let timer: number | null | NodeJS.Timer = null;
  function handleMoveEvent(event: MouseEvent) {
    if (!timer) onMove();
    clearTimeout(timer as number);
    timer = setTimeout(() => {
      onMoveEnd();
      timer = null;
    }, moveEndDelay);
  }

  function clear() {
    clearTimeout(timer as number);
    timer = null;
  }
  return {
    handleMoveEvent,
    clear,
  };
}
