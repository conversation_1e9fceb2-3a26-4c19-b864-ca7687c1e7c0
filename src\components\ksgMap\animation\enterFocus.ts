import { Group } from "three";
import { Tween, Easing } from "@tweenjs/tween.js";
import ctx from "../ctx";
import { KsgControls } from "../core/KsgControls";
/**
 * 进入子图动画
 */
export function viewMoveAnimation(
  controls: KsgControls,
  to: [number, number, number],
  duration: number = 500
) {
  const [cx, cy, cz] = getEndCameraPosition(to[1], to);
  return new Promise((resolve) => {
    new Tween({
      y: controls?.target.y!,
      x: controls?.target.x!,
      z: controls?.target.z!,
      xp: controls?.object.position.x!,
      yp: controls?.object.position.y!,
      zp: controls?.object.position.z!,
    })
      .to(
        {
          x: to[0],
          y: to[1] - 5, //上一次修改前，围绕y轴

          z: to[2],

          xp: cx,
          yp: cy - 12,
          zp: cz,
        },
        duration
      )
      .easing(Easing.Quadratic.InOut)
      .onUpdate(({ x, y, z, xp, yp, zp }) => {
        // controls?.target.set(0, y, 0);
        controls?.target.set(x, y, z);
        controls?.object.position.set(xp, yp, zp);
      })
      .onComplete(resolve)
      .start();
  });
}

/**计算相机位置 */
function getEndCameraPosition(
  y: number, //相机高度
  point: [number, number, number], //点的位置
  distance: number = 25
) {
  if (point[0] === 0 && point[2] === 0)
    return [Math.abs(distance), y, Math.abs(distance)];
  let pointR = Math.sqrt(point[0] ** 2 + point[2] ** 2 + (point[1] - y) ** 2);
  const sin = point[2] / pointR; //z
  const cos = point[0] / pointR; //x
  pointR += distance;
  return [pointR * cos, y + 8, pointR * sin];
}
