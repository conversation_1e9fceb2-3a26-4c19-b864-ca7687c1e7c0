{"name": "ksg-map", "version": "2.0.0", "private": true, "description": "一个集成了单个领域下单个根知识点和多个根知识点可视化3D图谱组件。", "keywords": ["map", "brabery", "KsgMap"], "repository": {"type": "git", "url": "https://tangrunping@**************:12301/r/bravery_3.0.git"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "module", "main": "ksgMap.js", "scripts": {"dev": "vite  --host", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@endlessorigin/KsgMap": "^2.0.0", "@tweenjs/tween.js": "^18.6.4", "@types/node": "^22.13.14", "@types/three": "^0.175.0", "lodash": "^4.17.21", "path": "^0.12.7", "three": "^0.175.0", "vite-plugin-dts": "^4.5.3", "vue": "^3.5.13"}, "devDependencies": {"@types/lodash": "^4.17.16", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}