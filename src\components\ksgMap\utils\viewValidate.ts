/**
 *检测坐标是否在可视区域内
 * @param {Object} view 视图区域
 * @param {Object} dnc dnc坐标位置
 */
export default function innerViewValidate(
  view: { minX: number; maxX: number; minY: number; maxY: number },
  dnc: { x: number; y: number }
): boolean {
  dnc.x += view.minX;
  dnc.y += view.minY;
  if (
    dnc.x < view.minX ||
    dnc.x > view.maxX ||
    dnc.y < view.minY ||
    dnc.y > view.maxY
  ) {
    return false;
  }
  return true;
}
