import ctx from "../ctx";
import type { FocusData, Point } from "../types";
import { renderFocusData } from "../core/renderData";
import { ENTER_FOCUS_MODE } from "../enums";
/**
 * 处理点击知识点回调事件
 * @param {Mesh} point 点击的知识节点
 * @param {ENTER_FOCUS_MODE} mode 进入模式,ENTER-进入子图,BACK-历史回退模式
 */
export function handleEnterFocus(
  point: Point,
  mode: ENTER_FOCUS_MODE = ENTER_FOCUS_MODE.ENTER
): Promise<void> {
  const { id } = point;
  //为当前栈顶节点不做任何操作
  if (
    mode === ENTER_FOCUS_MODE.ENTER &&
    ctx.focusStack!.length &&
    id === ctx.focusStack![ctx.focusStack!.length - 1]
  )
    return Promise.resolve();

  //获取数据(注意！！！！！！！！所有的数据源都从graph中获取)
  const realPoint = ctx.graph!.getPointById(id)!;

  //获取当前知识点的子知识点（直接前驱）和边数据
  const focusData: FocusData = {
    focusPoint: realPoint,
    rootPointId: id,
    // points: point.childIds.map((id) => ctx.pointsMesh!.getPointDataById(id)),
    points: realPoint.childIds.map((id) => ctx.graph!.getPointById(id)!),
  };

  //历史缓存策略
  if (mode !== ENTER_FOCUS_MODE.BACK) {
    if (ctx.focusStack!.length >= ctx.focusStackMaxSize! + 1) {
      const [rootId, ...others] = ctx.focusStack!;
      others.unshift();
      others.push(id);
      ctx.focusStack = [rootId, ...others];
    } else {
      ctx.focusStack!.push(id);
    }
  }
  // 渲染子图数据
  return renderFocusData(focusData);
}
