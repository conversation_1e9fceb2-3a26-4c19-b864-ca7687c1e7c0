import * as THREE from "three";
import type { Size, Point } from "../types";
import { type Context } from "../ctx";
export default function createHoverPointEventFun(
  elSize: Size,
  ctx: Partial<Context>,
  enterCallback: (data: Point) => void,
  leaveCallback: () => void
) {
  let width: number | null = elSize.width;
  let height: number | null = elSize.height;
  let raycaster: null | THREE.Raycaster = new THREE.Raycaster();

  //清空闭包造成的内存泄漏
  function clear() {
    width = null;
    height = null;
    raycaster = null;
  }

  // 更新尺寸
  function updateSize(w: number, h: number) {
    width = w;
    height = h;
  }
  //生成的event事件
  function event(e: MouseEvent) {
    if (!ctx.pointsMesh || ctx.controls?.isControls) return;
    const { offsetX, offsetY } = e;
    raycaster!.setFromCamera(
      new THREE.Vector2(
        (offsetX / width!) * 2 - 1,
        -(offsetY / height!) * 2 + 1
      ),
      ctx.camera!
    );
    const intersects = raycaster!.intersectObject(ctx.pointsMesh);
    let pointIndex: number | null = null;
    if (intersects.length > 0) {
      pointIndex = intersects[0].index as number; //节点索引
    }
    if (intersects[0]?.object && intersects[0].distance <= ctx.maxDistance!) {
      const id = ctx.pointsMesh.getPointData(pointIndex!)?.id;
      enterCallback(ctx.graph?.getPointById(id!)!);
    } else {
      leaveCallback();
    }
  }

  return {
    clear,
    event,
    updateSize,
  };
}
