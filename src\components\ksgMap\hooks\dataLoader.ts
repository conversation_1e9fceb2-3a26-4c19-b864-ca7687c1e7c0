import { POINT_STUDY_STATUS } from "../enums/index";
import type { PointData, getSignalRootApi } from "../types";
import type { Ref } from "@vue/reactivity";
import { reFormateTitle, injectMathJax } from "../utils/mathJax";

// const strs = [
//   "23213",
//   "撒大大的撒撒旦阿达的撒",
//   "大撒大撒",
//   "撒",
//   "撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒打撒打撒的撒的撒大萨达撒 的撒的撒大撒旦撒旦阿松大啊的撒大萨达撒阿大撒的撒大撒旦萨达撒的撒旦sad撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒大撒",
// ];
/**
 *处理单节点模式，处理分页获取额数据
 * @param api 获取请求的数据
 * @param rootId 根节点id
 * @param loading  loading状态
 * @param crt 从那层开始获取数据
 * @param levelSize  获取多少层
 */
export default function dataLoader(
  api: getSignalRootApi,
  rootId: string,
  loading: Ref<"loading" | "loaded" | "error">,
  crt: number = 1,
  levelSize: number = 2
) {
  // 节点数据暂存
  const cache: PointData[] = [];
  //   总节点数
  let totalPoints: number = 0;
  //记录节点数
  let pointCount: number = 0;

  async function init() {
    loading.value = "loading";
    try {
      await injectMathJax();
      const {
        data: { dataList, total },
      } = await api(crt, levelSize, rootId);
      cache.push(
        ...dataList.map((item) => {
          item.status = item.status ?? POINT_STUDY_STATUS.unstudy;
          item.pointName = reFormateTitle(item.pointName);
          return item;
        })
      );
      pointCount = cache.length;
      totalPoints = total;
      loading.value = "loaded";
    } catch (error) {
      console.error("响应数据异常！无法正常渲染", error);
      loading.value = "error";
    }
    return {
      data: cache,
      pager: {
        current: crt,
        levelSize,
        total: totalPoints,
      },
      rootId,
    };
  }

  async function loadMore(rootId: string, current: number, levelSize: number) {
    if (pointCount >= totalPoints) return;
    loading.value = "loading";
    try {
      const {
        data: { dataList },
      } = await api(current, levelSize, rootId);
      //  由于新的数据加载之前的存在的数据层级会变，重新计算之前的数据结构
      // dataList.forEach((newPoint) => {
      //   newPoint.pointName = reFormateTitle(newPoint.pointName);
      //   newPoint.status = newPoint.status ?? 0;
      //   // 新数据之前是否有返回，有返回则说明该数据层级会变
      //   const index = cache.findIndex(
      //     (oldPoint) => oldPoint.pointId === newPoint.pointId
      //   );
      //   index > -1
      //     ? cache[index].parentPointIds.push(...newPoint.parentPointIds)
      //     : cache.push(newPoint);
      // });
      loading.value = "loaded";
      return dataList;
    } catch (error) {
      console.error("响应数据异常！无法正常渲染");
      loading.value = "error";
    }

    return [];
  }

  /**
   * 新版本加载更多数据
   * @param rootId
   * @param current
   * @param levelSize
   * @returns
   */
  async function loadMoreNew(
    rootId: string,
    current: number,
    levelSize: number
  ) {
    if (pointCount >= totalPoints) return;
    loading.value = "loading";
    try {
      const {
        data: { dataList },
      } = await api(current, levelSize, rootId);
      loading.value = "loaded";
      return dataList;
    } catch (error) {
      console.error("响应数据异常！无法正常渲染");
      loading.value = "error";
    }
    return [];
  }

  return {
    init,
    loadMore,
    loadMoreNew,
  };
}
