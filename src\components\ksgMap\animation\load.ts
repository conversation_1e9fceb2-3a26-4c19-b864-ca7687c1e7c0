import { Tween } from "@tweenjs/tween.js";
import TWEEN from "@tweenjs/tween.js";
import { Mesh, Line, ShaderMaterial } from "three";
import { addAnimatedFlag, removeAnimatedFlag } from "./mark";

import KsgLine from "../core/KsgLine";
import KsgPoint from "../core/KsgPoints";
import { Point } from "../types";
type Option = {
  opacity: number;
  x: number;
  y: number;
  z: number;
};
/**
 * 初始化知识节点时渲染动画
 */
export function pointEnterAnimation(
  point: Point,
  pointsMesh: KsgPoint,
  option: Option,
  duration: number = 300
) {
  const { x, y, z } = option;

  return new Promise((resolve) => {
    new Tween({ opacity: 0, x: x, y: y - 7, z: z, scale: 0 })
      .to({ ...option, scale: 1 }, duration)
      .easing(TWEEN.Easing.Sinusoidal.Out)
      .onUpdate(({ x, y, z, opacity, scale }) => {
        pointsMesh.updateOpacity([point.index!], opacity);
        pointsMesh.updatePosition([point.index!], [x, y, z]);
      })
      .onComplete(() => {
        resolve(point);
      })
      .start();
  });
}

/**
 *初始化连线时加载动画
 *@param line 连线
 *@param startPoint 起点
 *@param endPoint 终点
 *@param opacity 透明度
 *@param duration 动画时长
 */
export function lineEnterAnimation(
  line: KsgLine,
  startPoint: Point,
  endPoint: Point,
  opacity: number,
  duration: number = 800
) {
  const [x0, y0, z0] = startPoint.coordinate;
  const [x1, y1, z1] = endPoint.coordinate;
  return new Promise((resolve) => {
    new Tween({ opacity: 0, x: x0, y: y0, z: z0 })
      .to({ opacity, x: x1, y: y1, z: z1 }, duration)
      .easing(TWEEN.Easing.Quadratic.InOut)
      .onUpdate(({ x, y, z, opacity }) => {
        line.updateEndPosition(endPoint.endPointsIndex!, [x, y, z]);
        line.updateOpacity(opacity);
      })
      .start()
      .onComplete(resolve);
  });
}

/**
 * 移除知识节点动画
 */
export function pointLeaveAnimation(
  mesh: any,
  duration: number = 500,
  option: { opacity: number; scale: number } = { opacity: 0, scale: 0 }
) {
  if (mesh.opacity <= option.opacity) return Promise.resolve(mesh);
  return new Promise((resolve) => {
    new Tween({ opacity: 1, scale: 1 })
      .to({ ...option }, duration)
      .onUpdate(({ opacity, scale }) => {
        mesh.setOpacity(opacity);
        mesh.scale.set(scale, scale, scale);
      })
      .onStart(() => {
        addAnimatedFlag(mesh);
      })
      .onComplete(() => {
        removeAnimatedFlag(mesh);
        resolve(mesh);
      })
      .start();
  });
}

/**
 * 移除连线动画
 */
export function lineLeaveAnimation(line: KsgLine, duration: number = 500) {
  return new Promise((resolve) => {
    new Tween({
      opacity: (line.material as ShaderMaterial).uniforms.uOpacity.value,
    })
      .to({ opacity: 0 }, duration)
      .onUpdate(({ opacity }) => {
        line.updateOpacity(opacity);
      })
      .onComplete(() => {
        resolve(line);
      })
      .start();
  });
}

export function getCrtLineOpacity(line: Line | Mesh) {
  if (Array.isArray(line.material)) {
    return line.material[0].opacity;
  } else {
    return line.material.opacity;
  }
}
