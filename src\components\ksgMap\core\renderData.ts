import ctx from "../ctx";
import { type Point, type FocusData } from "../types";
import {
  pointEnterAnimation,
  lineEnterAnimation,
  lineLeaveAnimation,
} from "../animation/load";
import { viewMoveAnimation } from "../animation/enterFocus";
import { updatePointPositionAnimation } from "../animation/loadMore";
import KsgPoint from "./KsgPoints";
import KsgLine from "./KsgLine";
import focusCrust from "./focusCrust";
import { focusLabel } from "./KsgLabel";
import { VIEW_MODE, MODE } from "../enums";
import type { DiffData, ModifyPoint } from "./KsgGraph";

//聚焦的直接前驱节点
let lastFocusChildPoints: Point[] = [];

/**
 * 第一次渲染单根节点数据（且聚焦根节点）
 */
export function firstRenderSignalRootPoints() {
  // 动画队列
  const points: Point[] = [];
  const pointAnimationQue: Point[][] = [];

  //   渲染知识节点数据
  const focusPoint = ctx.graph?.getPointById(ctx.focusPointInfo!.pointId)!;
  for (const levelKey in ctx.graph!.idLevelMap) {
    pointAnimationQue.push(
      ctx.graph!.idLevelMap[levelKey].map((id) => ctx.graph!.getPointById(id)!)
    );
  }
  lastFocusChildPoints = focusPoint.childIds.map(
    (id) => ctx.graph!.getPointById(id)!
  );

  ctx.graph?.pointsData.forEach((point) => points.push(point));
  // 渲染节点
  ctx.pointsMesh = new KsgPoint(points, ctx.pointsLevelPager!.total, 0, 20);
  ctx.viewGroup!.add(ctx.pointsMesh);
  ctx.focusChildrenIndex = new Set(
    lastFocusChildPoints.map((point) => point.index!)
  );
  // 处理聚焦节点
  const rootPoint = pointAnimationQue.shift()![0]; //弹出聚焦节点，不参与动画
  ctx.pointsMesh.toggleFocus(rootPoint.index!);
  focusCrust.display(rootPoint);
  focusLabel.display(rootPoint);
  ctx.focusStack?.push(rootPoint.id); //记录历史
  startPointAnimation(pointAnimationQue, 1200)
    .then(() => {
      ctx.focusLine = new KsgLine(rootPoint, lastFocusChildPoints, 0.1);
      ctx.viewGroup?.add(ctx.focusLine);
      return Promise.all(
        lastFocusChildPoints.map((point) =>
          lineEnterAnimation(ctx.focusLine!, rootPoint, point, 0.1)
        )
      );
    })
    .then(() => {
      ctx.pointsMesh?.toggleFocusChildren(
        lastFocusChildPoints.map((point) => point.index!)
      );
      ctx.focusLine!.enableAnimation = true;
    });
}

/**
 * 第一次渲染多根节点数据（没有焦点）
 */
export function firstRenderMultiplyRootPoints() {
  const points: Point[] = [];
  const pointAnimationQue: Point[][] = [];
  ctx.graph?.pointsData.forEach((point) => points.push(point));
  for (const levelKey in ctx.graph!.idLevelMap) {
    pointAnimationQue.push(
      ctx.graph!.idLevelMap[levelKey].map((id) => ctx.graph!.getPointById(id)!)
    );
  }
  // 渲染节点
  ctx.pointsMesh = new KsgPoint(
    [...points],
    ctx.pointsLevelPager!.total,
    0,
    20
  );
  ctx.pointsMesh.setHightLightPoints(points);
  ctx.viewGroup!.add(ctx.pointsMesh);
  ctx.focusStack!.push(points[0].id);
  startPointAnimation(pointAnimationQue, 1200, 1);
}

// 知识点初始化动画
function startPointAnimation(
  animationQue: Point[][],
  totalDuration: number = 600,
  initOpacity: number = 0.3
) {
  let resolve: Function;
  const p = new Promise((res) => (resolve = res));
  let index = 0;
  const peerLevelDuration =
    totalDuration / (animationQue.length > 5 ? 4 : animationQue.length);
  function peerTimeAnimation(anQue: Point[]) {
    if (index >= animationQue.length) return resolve();
    const ps: Promise<any>[] = [];
    anQue.forEach((point) => {
      const p: Promise<any> = pointEnterAnimation(
        point,
        ctx.pointsMesh!,
        {
          x: point.coordinate[0],
          y: point.coordinate[1],
          z: point.coordinate[2],
          opacity: initOpacity,
        },
        peerLevelDuration
      );
      ps.push(p);
    });
    Promise.all(ps).then(() => {
      peerTimeAnimation(animationQue[++index]);
    });
  }
  peerTimeAnimation(animationQue[index]);
  return p;
}

/**
 * 加载更多-渲染数据
 */
export function renderMoreData(focusIndex: number, diffData: DiffData) {
  ctx.pointsMesh?.loadMore(diffData.newPoints, 0);
  const loadedPoints = samePointsLevelToLevelArray(diffData.newPoints);
  const focusPointId = ctx.pointsMesh!.getPointData(focusIndex)!.id;
  // const focusPoint = ctx.pointsMesh!.getPointData(focusIndex)!;
  const focusPoint = ctx.graph!.getPointById(focusPointId)!;
  let updateLineFlag = false;
  // 加载后，当前聚焦的节点子节点已经加载完毕，准备连线动画
  if (!lastFocusChildPoints.length && focusIndex > -1) {
    lastFocusChildPoints = focusPoint.childIds.map(
      (id) => ctx.graph!.getPointById(id)!
    );
    ctx.focusLine = new KsgLine(focusPoint, lastFocusChildPoints, 0.1);
    ctx.viewGroup?.add(ctx.focusLine);
    updateLineFlag = true;
  }

  //添加高亮节点
  if (ctx.pointsMesh!.focusIndex > -1)
    ctx.pointsMesh?.setHightLightPoints(diffData.newPoints);

  //加载节点的状态
  const opacity =
    ctx.model == MODE.MULTIPLE_ROOT
      ? ctx.pointsMesh!.focusIndex > -1
        ? 0.3
        : 1.0
      : 0.3;

  const updatePointsAnimation: Promise<any>[] = [];
  diffData.updatePoints.forEach((pointInfo) => {
    updatePointsAnimation.push(
      updatePointPositionAnimation(
        ctx.pointsMesh!,
        ctx.focusLine!,
        focusCrust,
        focusLabel,
        pointInfo,
        lastFocusChildPoints
      )
    );
  });
  Promise.all(updatePointsAnimation)
    .then((points) => {
      points.forEach((point) => ctx.pointsMesh?.updatePointInfo(point));
    })
    .then(() => startPointAnimation(loadedPoints, 1000, opacity))
    .then(() =>
      Promise.all(
        lastFocusChildPoints.map((point) =>
          updateLineFlag
            ? lineEnterAnimation(ctx.focusLine!, focusPoint, point, 0.1)
            : null
        )
      )
    )
    .then(() => {
      ctx.focusLine!.enableAnimation = true;
      ctx.pointsMesh?.toggleFocusChildren(
        lastFocusChildPoints.map((point) => point.index!)
      );
    })
    .then(() => {
      if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW) {
        diffData.newPoints.map((point) =>
          ctx.pointsMesh?.enablePointBreath(point.index!)
        );
      }
    });
}

/**
 * 把动画数组按层次转换为二维数组(从顶到下)
 */
function samePointsLevelToLevelArray(points: Point[]) {
  const levelMap: { [key: number]: Point[] } = {};
  points.forEach((point: Point) => {
    if (!levelMap[point.level]) {
      levelMap[point.level] = [point];
    } else {
      levelMap[point.level].push(point);
    }
  });
  return Object.entries(levelMap).map((item) => item[1]);
}

/**
 *进入子图
 * @param focusInfo 聚焦节点信息
 */
export function renderFocusData(focusInfo: FocusData): Promise<void> {
  console.log("===>", ctx.graph);
  console.log("focusData", focusInfo);
  focusCrust.display(focusInfo.focusPoint);
  ctx.pointsMesh?.toggleFocus(focusInfo.focusPoint.index!);
  return Promise.resolve()
    .then(() => {
      // 关闭控制器
      ctx.controls!.enabled = false;

      if (ctx.focusLine) {
        lineLeaveAnimation(ctx.focusLine!, 300).then(() => {
          ctx.viewGroup?.remove(ctx.focusLine!);
          ctx.focusLine!.dispose();
        });
      }

      // 缓存
      lastFocusChildPoints = focusInfo.points;
      ctx.focusChildrenIndex = new Set(
        lastFocusChildPoints.map((point) => point.index!)
      );
      //移动视角
      return viewMoveAnimation(
        ctx.controls!,
        [
          focusInfo.focusPoint.coordinate[0],
          focusInfo.focusPoint.coordinate[1] + 6,
          focusInfo.focusPoint.coordinate[2],
        ],
        700
      )
        .then(() => focusLabel.display(focusInfo.focusPoint))
        .then(() => {
          ctx.controls!.enabled = true;
          ctx.focusLine = new KsgLine(
            focusInfo.focusPoint,
            lastFocusChildPoints,
            0.1
          );
          ctx.viewGroup?.add(ctx.focusLine);
          return Promise.all(
            lastFocusChildPoints.map((point) =>
              lineEnterAnimation(
                ctx.focusLine!,
                focusInfo.focusPoint,
                point,
                0.1
              )
            )
          );
        });
    })
    .then(() => {
      ctx.pointsMesh?.toggleFocusChildren(
        lastFocusChildPoints.map((point) => point.index!)
      );

      ctx.focusLine!.enableAnimation = true;
    });
}
