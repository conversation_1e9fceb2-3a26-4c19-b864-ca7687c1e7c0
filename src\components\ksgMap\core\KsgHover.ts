import {
  InstancedMesh,
  ShaderMaterial,
  PlaneGeometry,
  Vector2,
  Color,
  AdditiveBlending,
  Vector3,
  FrontSide,
} from "three";
import { Point } from "../types";
import { studyStatusToColor } from "../utils";
import vertexShader from "../shader/haloVert.glsl?raw";
import fragmentShader from "../shader/haloFrag.glsl?raw";
import { Context } from "../ctx";

// 共享hover平面
const geo = new PlaneGeometry(6, 6);
class KsgHoverCircle extends InstancedMesh {
  /*上一个hover的ksgPoint */
  lastIndex: number = -1;
  constructor(size: number = 6) {
    super(
      geo,
      new ShaderMaterial({
        uniforms: {
          uTime: { value: 0.0 },
          uSpriteSize: { value: new Vector2(size, size) }, // 设置为sprite的实际尺寸
          uSpeed: { value: 0.5 }, // 控制扩散速度的系数，默认值设为0.5以减慢速度
          uMinRadius: { value: 0.5 }, // 最小半径，单位是像素
          uNumRings: { value: 10.0 }, // 指定想要显示的圆环数量
          uRingDuration: { value: 2.0 }, // 单个圆环的持续时间为1秒
          uDelay: { value: 1.0 },
          uColor: { value: new Color(0xffffff) },
        },
        vertexShader,
        fragmentShader,
        transparent: true,
        side: FrontSide,
        depthWrite: true,
        depthTest: true, // 保持深度测试开启
        blending: AdditiveBlending, // 使用正常混合模式
      }),
      1
    );
    this.renderOrder = 4;
  }

  /**
   *把该模型绑定到hover节点
   *@param {KsgPoint} bindPoint 要绑定的节点的userData
   */
  display(bindPoint: Point) {
    if (this.lastIndex === bindPoint.index) return;
    (this.material as ShaderMaterial).uniforms.uTime.value = 0;
    this.position.set(...bindPoint.coordinate);
    (this.material as ShaderMaterial).uniforms.uColor.value =
      studyStatusToColor(bindPoint.status);
    this.visible = true;
    this.lastIndex = bindPoint.index!;
  }

  /**
   *移出hover状态
   */
  hide() {
    this.visible = false;
    this.lastIndex = -1;
  }

  /**
   *动画更新函数
   */
  update(ctx: Partial<Context>, delta: number = 0.01) {
    if (!this.visible) return;
    (this.material as ShaderMaterial).uniforms.uTime.value += delta;
    const v3 = new Vector3(
      ctx.camera!.position.x,
      ctx.camera!.position.y,
      ctx.camera!.position.z
    );
    const crtWorldPosition = new Vector3();
    this.getWorldPosition(crtWorldPosition);
    // 初始为法线
    v3.sub(ctx.controls!.target).normalize();
    this.lookAt(
      new Vector3(
        v3.x / 2 + crtWorldPosition.x,
        v3.y / 2 + crtWorldPosition.y,
        v3.z / 2 + crtWorldPosition.z
      )
    );
  }

  /**
   * 释放内存
   */
  free() {
    this.geometry.dispose();
    (this.material as ShaderMaterial).dispose();
  }
}

const ksgHover = new KsgHoverCircle();
ksgHover.visible = false;

export default ksgHover;
