import { PerspectiveCamera } from "three";
import ctx from "../ctx";
import type { CameraConfig } from "../types/index";

/**
 * 相机配置函数 - 创建和配置Three.js透视相机
 *
 * 透视相机是Three.js中最常用的相机类型，模拟人眼的视觉效果
 * 具有近大远小的透视效果，适合3D场景的渲染
 *
 * @param config 相机配置参数对象
 * @returns 返回包含相机实例的对象
 */
export default function useCamera(
  config: CameraConfig = {
    // 视野角度(Field of View) - 决定相机的视野范围
    // 45度是一个常用的默认值，提供自然的视觉效果
    fov: 45,

    // 宽高比(Aspect Ratio) - 渲染画布的宽度除以高度
    // 必须与实际画布尺寸匹配，否则会出现拉伸变形
    // aspect: 1.778, // 16:9比例 (1920/1080)
    aspect: 1200 / 675, // 默认画布尺寸比例 (约1.78:1)

    // 近裁剪面(Near Clipping Plane) - 相机能看到的最近距离
    // 小于此距离的物体将不会被渲染，0.1是常用的最小值
    near: 0.1,

    // 远裁剪面(Far Clipping Plane) - 相机能看到的最远距离
    // 超过此距离的物体将不会被渲染，1000适合大多数场景
    far: 1000,

    // 相机初始位置 - 在3D空间中的坐标
    position: {
      x: 30.2,  // X轴位置 - 左右方向
      y: -3.14, // Y轴位置 - 上下方向（负值表示在原点下方）
      z: 24.98, // Z轴位置 - 前后方向（正值表示远离原点）
    },
  }
) {
  // 创建透视相机实例
  // PerspectiveCamera构造函数参数：(fov, aspect, near, far)
  const camera = new PerspectiveCamera(
    config.fov,    // 视野角度
    config.aspect, // 宽高比
    config.near,   // 近裁剪面
    config.far     // 远裁剪面
  );

  // 将相机实例保存到全局上下文中，供其他模块使用
  ctx.camera = camera;

  // 设置相机的初始位置
  // position.set(x, y, z) 是Three.js中设置3D对象位置的标准方法
  camera.position.set(
    config.position?.x!, // X坐标
    config.position?.y!, // Y坐标
    config.position?.z!  // Z坐标
  );

  // 返回相机实例，供调用者使用
  return {
    camera,
  };
}
