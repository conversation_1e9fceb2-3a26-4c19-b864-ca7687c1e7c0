import ctx from "../ctx";
/**
 * 画布尺寸更新
 * @param wrapperEle 画布容器元素
 */
export default function useUpdate(wrapperEle: HTMLElement) {
  if (!ctx.renderer || !ctx.css2dRenderer || !ctx.camera) return;
  const { width, height } = wrapperEle.getBoundingClientRect();
  ctx.renderer?.setSize(width, height);
  ctx.css2dRenderer?.setSize(width, height);
  ctx.camera!.aspect = width / height;
  ctx.camera?.updateProjectionMatrix();
  const pixcel = window.devicePixelRatio;
  ctx.viewRange!.minX = wrapperEle.offsetLeft * pixcel;
  ctx.viewRange!.minY = wrapperEle.offsetTop * pixcel;
  ctx.viewRange!.maxX = ctx.viewRange!.minX + width * pixcel;
  ctx.viewRange!.maxY = ctx.viewRange!.minY + height * pixcel;
  return {
    width,
    height,
  };
}
