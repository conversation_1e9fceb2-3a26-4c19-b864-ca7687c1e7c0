import clonePoint from "../utils/clonePoint";
import type { PointData, Point } from "../types";
import FrameScheduler from "../utils/FrameScheduler";

// 全局任务调度器实例 - 用于管理异步计算任务
// 避免长时间的同步计算阻塞主线程，提供更流畅的用户体验
export const frameScheduler = new FrameScheduler();

/**
 * 节点变化数据类型 - 用于记录节点在更新过程中的状态变化
 * 主要用于增量更新和动画过渡效果
 */
export type ModifyPoint = {
  /** 更新前的节点状态 - 保存原始位置和属性 */
  old: Point;
  /** 更新后的节点状态 - 保存新的位置和属性（可选） */
  new?: Point;
};

/**
 * 差异数据类型 - 用于描述数据更新时的变化情况
 * 支持增量更新，避免重新计算整个图结构
 */
export type DiffData = {
  /** 新增的节点列表 - 首次加载或新增的节点 */
  newPoints: Point[];
  /** 位置发生变化的节点映射 - key为节点ID，value为变化前后的状态 */
  updatePoints: Map<string, ModifyPoint>;
};

/**
 * 知识图谱空间布局计算器 - KsgGraph类
 *
 * 这是知识图谱的核心计算引擎，负责将原始的节点数据转换为3D空间中的坐标布局
 * 实现了DAG（有向无环图）的层次化布局算法，确保知识节点按照逻辑关系有序排列
 *
 * 主要功能：
 * - 解析节点间的父子关系，构建图结构
 * - 计算节点的层级分布（BFS层次遍历）
 * - 计算每个节点在3D空间中的精确坐标
 * - 支持增量数据加载和位置更新
 * - 提供异步计算能力，避免阻塞UI线程
 *
 * 算法特点：
 * - 使用广度优先搜索确定节点层级
 * - 同层节点均匀分布，避免重叠
 * - 支持多根节点的复杂图结构
 * - 优化的增量更新算法，提高性能
 */
export default class KsgGraph {
  /**
   * 节点数据映射表 - 存储所有处理后的节点信息
   * key: 节点ID, value: 包含坐标和关系信息的Point对象
   */
  pointsData: Map<string, Point> = new Map();

  /**
   * 层级索引映射 - 按层级组织节点ID
   * key: 层级编号(0,1,2...), value: 该层级的所有节点ID数组
   * 用于快速查找同层节点和层级遍历
   */
  idLevelMap: { [level: number]: string[] } = {};

  /**
   * 增量更新的差异数据 - 记录数据变化情况
   * 用于支持懒加载和动画过渡效果
   */
  diffData: DiffData = { newPoints: [], updatePoints: new Map() };

  /**
   * 构造函数 - 初始化知识图谱布局计算器
   *
   * @param pointsData 原始节点数据数组，包含节点信息和父子关系
   */
  constructor(pointsData: PointData[]) {
    // 立即开始计算节点布局
    this.compute(pointsData);
  }

  /**
   * 计算节点布局 - 主要的布局计算流程
   *
   * 这是图布局计算的主入口，按照以下步骤进行：
   * 1. 构建图结构（解析节点关系）
   * 2. 计算节点层级（BFS遍历）
   * 3. 计算节点3D坐标（异步执行）
   *
   * @param pointsData 原始节点数据数组
   */
  compute(pointsData: PointData[]) {
    // 第一步：构建图数据结构
    // 将原始数据转换为内部的Point对象，建立父子关系映射
    this.build(pointsData);

    // 第二步：计算节点层级
    // 使用BFS算法确定每个节点在图中的层级位置
    this.computeLevel(this.pointsData);

    // 第三步：异步计算节点的3D空间坐标
    // 使用任务调度器避免长时间计算阻塞UI线程
    frameScheduler.addTask(() => {
      this.computePointPosition();
      return false; // 返回false表示任务完成
    });
  }
  /**
   * 加载更多数据
   * @param pointsData 节点数据
   */
  loadMore(pointsData: PointData[]): Promise<DiffData> {
    const diffData: DiffData = {
      newPoints: [],
      updatePoints: new Map(),
    };
    this.diffData = diffData;

    pointsData
      .map((point) => {
        let oldPoint = this.pointsData.get(point.pointId)!;
        if (oldPoint) {
          oldPoint = clonePoint(oldPoint);
          diffData.updatePoints.set(oldPoint.id, {
            old: oldPoint,
          });
        }
        return point?.pointId ?? null;
      })
      .filter((point) => point);
    //处理数据
    this.build(pointsData);
    this.computeLevel(this.pointsData);

    frameScheduler.addTask(() => {
      this.computePointPosition();
      return false;
    });
    // 位置计算完处理
    return new Promise((resolve) => {
      frameScheduler.onCompleted(() => {
        pointsData.forEach(({ pointId }) => {
          if (diffData.updatePoints.has(pointId)) {
            diffData.updatePoints.get(pointId)!.new =
              this.pointsData.get(pointId)!;
          } else {
            diffData.newPoints.push(this.pointsData.get(pointId)!);
          }
        });
        // console.log("diffData", diffData);
        resolve(diffData);
      });
    });
  }

  /**
   * 把数据维护到pointsDataMap中
   * 父关系转为子关系
   * @param pointsData 节点数据
   */
  private build(pointsData: PointData[]) {
    frameScheduler.addTask(() => {
      for (const point of pointsData) {
        let index = -1;
        const oldPoint = this.pointsData.get(point.pointId);
        if (oldPoint) {
          const cloneOldPoint = clonePoint(oldPoint);
          this.diffData.updatePoints.set(point.pointId, {
            old: cloneOldPoint,
          });
          oldPoint!.parentIds.push(...point.parentPointIds);
        } else {
          this.pointsData.set(point.pointId, {
            id: point.pointId,
            name: point.pointName,
            parentIds: point.parentPointIds,
            childIds: [],
            level: -1,
            coordinate: [0, 0, 0],
            isMilestone: point.isMilestone,
            status: point.status,
            index,
          });
        }
      }
      return false;
    });
    frameScheduler.addTask(() => {
      for (const point of pointsData) {
        const { pointId, parentPointIds } = point;
        for (const parentId of parentPointIds) {
          this.pointsData.get(parentId)?.childIds.push(pointId);
        }
      }
      return false;
    });
  }

  /**
   * 计算层级
   */
  computeLevel(points: Map<string, Point>) {
    this.idLevelMap = {};
    let degrees: { [key: string]: number } = {};
    let queue: string[] = [];
    const scope = this;

    // const taskCount = 10; // 分出的任务数量
    // frameScheduler.addTask(() => {

    //   return false;
    // });
    /**
     * 使用一个task来进行任务分块
     */

    frameScheduler.addTask(() => {
      scope.idLevelMap = {};
      degrees = {};
      queue = [];
      scope.pointsData.forEach((point) => {
        for (const childId of point.childIds) {
          if (!degrees[childId]) degrees[childId] = 0;
          degrees[childId]++;
        }

        return false;
      });

      return false;
    });

    // 计算第一层
    let startLevel = 0;
    frameScheduler.addTask(() => {
      scope.pointsData.forEach((point) => {
        const { id } = point;
        if (!degrees[id]) {
          queue.push(id);
          point.level = startLevel;
          if (!this.idLevelMap[startLevel]) this.idLevelMap[startLevel] = [];
          this.idLevelMap[startLevel].push(id);
        }
      });

      return false;
    });

    frameScheduler.addTask(() => {
      // 遍历所有节点，计算层级
      while (queue.length) {
        const id = queue.shift()!;
        const point = scope.pointsData.get(id)!;
        for (const childId of point.childIds) {
          degrees[childId]--;
          if (degrees[childId] === 0) {
            const crtLevel = scope.pointsData.get(id)!.level + 1;
            points.get(childId)!.level = crtLevel;
            queue.push(childId);
            if (!scope.idLevelMap[crtLevel]) scope.idLevelMap[crtLevel] = [];

            scope.idLevelMap[crtLevel].push(childId); //维护到idLevelMap中
          }
        }
      }
      // 判环
      if (Object.values(degrees).some((item) => item > 0)) {
        throw new Error("当前数据结构存在环");
      }
      return false;
    });
  }

  /**
   * 计算每层节点的位置
   */
  computePointPosition(levelHeight: number = 15, pointSpace: number = 7) {
    const levels = Object.keys(this.idLevelMap).map((key) => Number(key));
    levels.forEach((level, index) => {
      frameScheduler.addTask(() => {
        const crtLevelPointIds = this.idLevelMap[level];
        let y = -level * levelHeight;

        let count = crtLevelPointIds.length;

        if (count === 1) {
          const id = crtLevelPointIds[0];

          if (
            this.diffData.updatePoints.has(id) &&
            this.isPositionChange(id, [0, y, 0])
          ) {
            this.diffData.updatePoints.get(id)!.new = this.pointsData.get(id)!;
          } else if (this.isPositionChange(id, [0, y, 0])) {
            this.diffData.updatePoints.set(id, {
              old: clonePoint(this.pointsData.get(id)!),
              new: this.pointsData.get(id)!,
            });
          }
          this.pointsData.get(id)!.coordinate = [0, y, 0];
        } else {
          const interval = 3;
          let index = 0;
          for (let round = 1, s = count; s != 0; ++round) {
            let num = interval * round;
            if (num < s) {
              s -= num;
            } else {
              num = s;
              s = 0;
            }
            //重新计算y值
            y += Math.sin(round);
            const r = round * pointSpace;
            for (let i = 0; i < num; ++i) {
              const x = r * Math.cos((2 * Math.PI * i) / num);
              const z = r * Math.sin((2 * Math.PI * i) / num);
              if (index < count) {
                const id = crtLevelPointIds[index++];
                if (
                  this.diffData.updatePoints.has(id) &&
                  this.isPositionChange(id, [x, y, z])
                ) {
                  this.diffData.updatePoints.get(id)!.new =
                    this.pointsData.get(id)!;
                } else if (this.isPositionChange(id, [x, y, z])) {
                  this.diffData.updatePoints.set(id, {
                    old: clonePoint(this.pointsData.get(id)!),
                    new: this.pointsData.get(id)!,
                  });
                }
                this.pointsData.get(id)!.coordinate = [x, y, z];
              }
            }
          }
        }
        return index === levels.length - 1;
      });
    });
  }

  /**
   * 是否发生位置变化
   */
  private isPositionChange(id: string, coordinate: [number, number, number]) {
    const [x, y, z] = this.pointsData.get(id)?.coordinate ?? [0, 0, 0];
    if (
      (!coordinate[0] && !coordinate[1] && !coordinate[2]) ||
      (!x && !y && !z)
    )
      return false;
    return x !== coordinate[0] || y !== coordinate[1] || z !== coordinate[2];
  }

  /**
   * 根据id获取Point
   */
  getPointById(id: string): Point | null {
    return this.pointsData.get(id) ?? null;
  }

  /**
   * 获取当前图的最大层级
   * 从0层起
   */
  getMaxLevel() {
    return Math.max(...Object.keys(this.idLevelMap).map((key) => Number(key)));
  }
}
