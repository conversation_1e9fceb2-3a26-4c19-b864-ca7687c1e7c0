import ctx from "../ctx";
import TWEEN from "@tweenjs/tween.js";
import { Clock } from "three";
import KsgHover from "../core/KsgHover";
import focusCrust from "../core/focusCrust";
export default function useRenderFrame() {
  const clock = new Clock();
  function startRenderFrame(time: any = 0) {
    const deltaTime = clock.getDelta();
    // updateVisible(ctx.viewGroup!, ctx.camera!);
    //label标签渲染
    ctx.css2dRenderer?.render(ctx.scene!, ctx.camera!);
    //后处理渲染通道和场景渲染
    // ctx.composer?.render();
    ctx.renderer?.render(ctx.scene!, ctx.camera!);
    //控制器
    ctx.controls?.update(deltaTime);
    ctx.controls?.autoRotateUpdate(deltaTime);
    // 动画
    TWEEN.update(time);
    requestAnimationFrame(startRenderFrame);
    //节点动画
    KsgHover.update(ctx, deltaTime);
    focusCrust.update(deltaTime);
    if (ctx.pointsMesh) ctx.pointsMesh.update();
    if (ctx.focusLine) ctx.focusLine?.update();
  }
  return {
    startRenderFrame,
  };
}
