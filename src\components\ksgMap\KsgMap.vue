<template>
  <div
    ref="container"
    :style="{
      height: addUnit(props.height),
      width: addUnit(props.width),
    }"
    class="ksg-three-container"
  >
    <div class="btn-container">
      <span class="back" @click="isFullScreen = !isFullScreen">
        <img width="20px" height="20px" v-show="!isFullScreen" title="全屏" src="./assets/images/full-icon.svg" alt="">
        <img width="20px" height="20px" v-show="isFullScreen" title="退出全屏" src="./assets/images/exitFull-icon.svg" alt=""></img>
      </span>
      <span class="back" @click="focusBack()">
        <img width="20px" height="20px" title="回退" src="./assets/images/back.svg" />
      </span>
      <span
        class="back"
        @click="focusBackToRoot"
        v-if="props.config.model === MODE.Single_ROOT"
      >
        <img width="20px" height="20px" title="回到根节点" src="./assets/images/top.svg" />
      </span>
    </div>
    <div class="loading" v-if="loadTip">
      <span class="loading-icon" v-if="props.loading === 'loading'">
        <img width="20px" height="20px" src="./assets/images/loading.svg"
      /></span>
      <span v-if="props.loading === 'error'">
        <img width="20px" height="20px" src="./assets/images/error.svg"
      /></span>
      <span> {{ loadTip }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  defineProps,
  defineEmits,
  defineExpose,
  type PropType,
  onMounted,
  onBeforeUnmount,
  ref,
  watch,
  nextTick,
} from "vue";
import { addUnit } from "./utils";
import useScene from "./config/scene";
import useCamera from "./config/camera";
import useRenderer from "./config/renderer";
import useCSS2DRender from "./config/css2dRenderer";
import useInitEvents from "./config/event";
import useRenderFrame from "./hooks/useRendererFrame";
import ctx from "./ctx";
import useControls from "./config/controls";
import { loadPointsData, loadMorePointsData } from "./core/loadData";
import { injectMathJax } from "./utils/mathJax";
import updateCanvasSize from "./config/update";
import { MODE, LOAD_STATUS } from "./enums";
import {useInitThreeJsConfig ,type Options } from "./config/index";
import type { PointData } from "./types";
const props = defineProps({
  // 组件尺寸
  width: {
    type: [Number, String],
    default: 1200,
    required: false,
  },
  height: {
    type: [Number, String],
    default: 675,
    required: false,
  },
  // threeJS相关配置
  config: {
    type: Object as PropType<Options>,
    default: () => {
      return {};
    },
    required: false,
  },
  // 加载状态
  loading: {
    type: String as PropType<"loading" | "loaded" | "error">,
    default: "loading",
  },
});
const emit = defineEmits<{
  (e: "loadMore", rootId: string, current: number, levelSize: number): void;
  (e: "clickLabel", id: string): void;
}>();

const container = ref<HTMLElement>();
const isFullScreen = ref(false);

const {
  cameraConfig,
  renderConfig,
  sceneConfig,
  controlsConfig,
  wrapperEleSizeConfig,
} = useInitThreeJsConfig({
  ...props.config,
  camera: {
    aspect: (props.width as number) / (props.height as number),
    ...props.config.camera,
  },
});
const { rendererDom } = useRenderer(renderConfig!);
const { camera } = useCamera(cameraConfig!);
const { css2dRendererDom } = useCSS2DRender(renderConfig!);
const { scene } = useScene(sceneConfig!);
const { controls } = useControls(controlsConfig!);
const {
  initEvents,
  destroyEvents,
  focusBackToRoot,
  focusBack,
  updateClickEventSize,
  updateHoverEventSize,
  changeLoadStatus,
} = useInitEvents(wrapperEleSizeConfig, {
  loadMoreCallback: (rootId, current, levelSize) =>
    emit("loadMore", rootId, current, levelSize),
  clickLabelCallback: (id) => emit("clickLabel", id),
});

//初始化整个场景
async function init() {
  container.value!.appendChild(rendererDom!);
  container.value!.appendChild(css2dRendererDom!);
  initEvents(container.value!);
  const { startRenderFrame } = useRenderFrame();
  // 开启循环渲染帧
  startRenderFrame();
  const observe = new ResizeObserver(async () => {
    await nextTick(() => {});
    const { width, height } = updateCanvasSize(container.value!)!;
    updateClickEventSize(width, height);
    updateHoverEventSize(width, height);
    if (
      props.width === width &&
      props.height === height &&
      isFullScreen.value
    ) {
      isFullScreen.value = false;
    }
  });
  observe.observe(container.value!);
}

watch(isFullScreen, (val: boolean) => {
  if (val) {
    container.value?.requestFullscreen();
  } else {
    document.exitFullscreen();
  }
});

const loadTip = ref("加载中...");
watch(
  () => props.loading,
  (val: string) => {
    switch (val) {
      case "loading":
        loadTip.value = "加载中...";
        changeLoadStatus(LOAD_STATUS.loading);
        break;
      case "loaded":
        loadTip.value = "";
        changeLoadStatus(LOAD_STATUS.loaded);
        break;
      case "error":
        loadTip.value = "加载出错！请检查网络~";
        changeLoadStatus(LOAD_STATUS.error);
        break;
    }
  }
);
// 销毁场景
function destroy() {
  destroyEvents();
  ctx.scene?.remove(ctx.scene!);
}
onMounted(init);
onBeforeUnmount(destroy);

defineExpose({
  firstLoadPointsData:async (pointsData: PointData[],total:number,rootId?:string)=>{
    await injectMathJax();
   return loadPointsData(pointsData,total,rootId);
  },
  loadMorePointsData,
  injectMathJax,
});
</script>

<style scoped>
.ksg-three-container {
  border-radius: 5px;
  overflow: hidden;
  position: relative;
  transition: all 0.25s;
}

.btn-container {
  position: absolute;
  right: 0px;
  bottom: 0px;
  background: rgba(0, 0, 0, 0.644);
  height: 35px;
  display: flex;
  align-items: center;
  border-top-left-radius: 10px;
}
.btn-container img{
  color: aqua;
}
.loading {
  position: absolute;
  background: rgba(0, 0, 0, 0.363);
  left: 0px;
  bottom: 0px;
  height: 35px;
  padding: 0px 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-top-right-radius: 10px;
  color: white;
  user-select: none;
}
.loading img {
  vertical-align: middle;
}
.loading-icon {
  animation: loading 1s linear infinite;
}
@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.back {
  color: rgba(255, 255, 255, 0.829);
  font-size: 20px;
  margin: 10px;
  cursor: pointer;
  z-index: 9999;
  user-select: none;
}
.back:hover {
  color: rgba(255, 255, 255, 1);
}
.back:active {
  color: rgba(255, 255, 255, 0.349);
}
@font-face {
  font-family: "alibaba-PuHuiTi";
  src: url(./assets/font/font.woff);
}
</style>
